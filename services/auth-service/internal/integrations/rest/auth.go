package rest

import (
	"auth-service/core/config"
	"fmt"
	"io"
	"net/http"
)

func DeleteUserFromTransactionService(token string, userId uint) error {
	// Create HTTP request
	req, err := http.NewRequest("DELETE", config.Get().TransactionService.Host+"api/v1/user/"+fmt.Sprintf("%d", userId), nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Add Authorization header
	req.Header.Set("Authorization", "Bearer "+token)

	// Execute the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read and decode the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("%s", string(body))
	}
	return nil
}
