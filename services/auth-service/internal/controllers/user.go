package controllers

import (
	"auth-service/core/connect"
	"auth-service/internal/constants"
	dto2 "auth-service/internal/dto"
	"auth-service/internal/middleware"
	"auth-service/internal/models"
	"auth-service/internal/services"
	"auth-service/utils/parsers"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserController struct {
	service services.UserServiceInterface
}

func NewUserController(service services.UserServiceInterface) *UserController {
	return &UserController{
		service: service,
	}
}

func (uc *UserController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	g.POST("login", AppHandler(uc.Login).Handle)
	g.POST("logout", middleware.Authentication(uc.service, constants.AllRole), A<PERSON><PERSON><PERSON><PERSON>(uc.Logout).Handle)
	g.POST("change-password", middleware.Authentication(uc.service, constants.AllRole), AppHandler(uc.ChangePassword).Handle)
	g.POST("create-user", middleware.Authentication(uc.service, constants.AdminRole), AppHandler(uc.CreateUser).Handle)
	g.POST("reset-password/:user_id", middleware.Authentication(uc.service, constants.AdminRole), AppHandler(uc.ResetPassword).Handle)
	g.GET("get-all", middleware.Authentication(uc.service, constants.AllRole), AppHandler(uc.GetAllUsers).Handle)
	g.GET("get-one/:user_id", middleware.Authentication(uc.service, constants.AdminRole), AppHandler(uc.GetOneUser).Handle)
	g.GET("get-all-admins", middleware.Authentication(uc.service, constants.TransactionServiceRole), AppHandler(uc.GetAllAdmins).Handle)
	g.PATCH("update-user/:user_id", middleware.Authentication(uc.service, constants.SuperAdmin), AppHandler(uc.UpdateUser).Handle)
	g.PATCH("update-my-profile", middleware.Authentication(uc.service, constants.AdminRole, constants.Accountant), AppHandler(uc.UpdateMyProfile).Handle)
	g.DELETE("delete-user/:user_id", middleware.Authentication(uc.service, constants.SuperAdmin), AppHandler(uc.DeleteUser).Handle)
	// g.DELETE("me", middleware.Authentication(uc.service, constants.AllRole), AppHandler(uc.DeleteMyProfile).Handle)
	g.GET("get-my-profile", middleware.Authentication(uc.service, constants.AdminRole, constants.Accountant, constants.SuperAdmin), AppHandler(uc.GetMyProfile).Handle)
	return g
}

func (uc *UserController) DeleteMyProfile(ctx *gin.Context) *AppResp {
	username, _ := ctx.Get("username")

	user, err := uc.service.GetByUsername(username.(string))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}

	if err := uc.service.DeleteUser(user.ID, user.ID); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "update failed",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "deleted successfully")
}

func (uc *UserController) UpdateMyProfile(ctx *gin.Context) *AppResp {
	userIDValue, exists := ctx.Get("user_id")
	if !exists {
		return &AppResp{
			Error:   "user_id not found in context",
			Message: "unauthorized",
			Code:    http.StatusUnauthorized,
		}
	}

	userID, ok := userIDValue.(uint)
	if !ok {
		return &AppResp{
			Error:   "invalid user_id type",
			Message: "unauthorized",
			Code:    http.StatusUnauthorized,
		}
	}

	user, err := uc.service.GetById(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}

	var input dto2.UpdateUserDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid request body",
			Code:    http.StatusBadRequest,
		}
	}

	if err := uc.service.UpdateUser(user, input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "update failed",
			Code:    http.StatusInternalServerError,
		}
	}

	return Ok(ctx, "updated successfully")
}

func (uc *UserController) GetMyProfile(ctx *gin.Context) *AppResp {
	userIDValue, exists := ctx.Get("user_id")
	if !exists {
		return &AppResp{
			Error:   "user_id not found in context",
			Message: "unauthorized",
			Code:    http.StatusUnauthorized,
		}
	}

	userID, ok := userIDValue.(uint)
	if !ok {
		return &AppResp{
			Error:   "invalid user_id type",
			Message: "unauthorized",
			Code:    http.StatusUnauthorized,
		}
	}

	user, err := uc.service.GetById(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}

	profileDto := dto2.MapUserToProfileDto(user)

	return Ok(ctx, profileDto)
}

func (uc *UserController) DeleteUser(ctx *gin.Context) *AppResp {
	role, _ := ctx.Get("role")
	id := ctx.Param("user_id")
	userId, _ := ctx.Get("user_id")

	user, err := uc.service.GetById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}
	if role.(string) == user.Role {
		return &AppResp{
			Error:   "denied",
			Message: "denied",
			Code:    http.StatusForbidden,
		}
	}

	if err := uc.service.DeleteUser(user.ID, userId.(uint)); err != nil {
		var appError struct {
			Code    int    `json:"Code"`
			Error   string `json:"Error"`
			Message string `json:"Message"`
		}
		if enErr := json.Unmarshal([]byte(err.Error()), &appError); enErr != nil {
			return &AppResp{
				Error:   err.Error(),
				Message: "delete failed",
				Code:    http.StatusInternalServerError,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: appError.Error,
			Code:    appError.Code,
		}
	}

	return Ok(ctx, "deleted successfully")
}

func (uc *UserController) UpdateUser(ctx *gin.Context) *AppResp {
	role, _ := ctx.Get("role")
	id := ctx.Param("user_id")

	user, err := uc.service.GetById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}
	if role.(string) == user.Role {
		return &AppResp{
			Error:   "denied",
			Message: "denied",
			Code:    http.StatusForbidden,
		}
	}

	var input dto2.UpdateUserDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid request body",
			Code:    http.StatusBadRequest,
		}
	}

	if err := uc.service.UpdateUser(user, input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "update failed",
			Code:    http.StatusInternalServerError,
		}
	}

	return Ok(ctx, "updated successfully")
}

func (uc *UserController) Login(ctx *gin.Context) *AppResp {
	var input dto2.UserInput
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	tokenDetails, err := uc.service.LoginService(models.User{
		Username: input.Username,
		Password: input.Password,
	})
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	return Ok(ctx, tokenDetails)
}

func (uc *UserController) Logout(ctx *gin.Context) *AppResp {
	username, _ := ctx.Get("username")
	if err := uc.service.DeleteToken(username.(string)); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "failed to delete token",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "successfully logged out")
}

func (uc *UserController) CreateUser(ctx *gin.Context) *AppResp {
	var inputDto dto2.CreateUserDto
	if err := ctx.ShouldBindJSON(&inputDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid input",
			Code:    http.StatusBadRequest,
		}
	}
	role, _ := ctx.Get("role")
	if inputDto.Role == role.(string) {
		return &AppResp{
			Error:   "forbidden",
			Message: "forbidden",
			Code:    http.StatusForbidden,
		}
	}
	user, _ := uc.service.GetByUsername(inputDto.Username)
	if user != nil {
		return &AppResp{
			Error:   "user not found",
			Message: "user not found",
			Code:    http.StatusBadRequest,
		}
	}
	createdUser, err := uc.service.CreateUser(&inputDto)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, createdUser)
}

func (uc *UserController) GetOneUser(ctx *gin.Context) *AppResp {
	role, _ := ctx.Get("role")
	id := ctx.Param("user_id")
	user, err := uc.service.GetById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}
	if role.(string) == user.Role {
		return &AppResp{
			Error:   "denied",
			Message: "denied",
			Code:    http.StatusForbidden,
		}
	}
	userResp := dto2.UsersRespDto{
		ID:       user.ID,
		Username: user.Username,
		Role:     user.Role,
		Name:     user.Name,
		LastName: user.LastName,
		Email:    user.Email,
	}
	return Ok(ctx, userResp)
}

func (uc *UserController) GetAllUsers(ctx *gin.Context) *AppResp {
	role, _ := ctx.Get("role")
	userId, _ := ctx.Get("user_id")
	var pag dto2.PaginationDto
	if err := ctx.ShouldBindQuery(&pag); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid input",
			Code:    http.StatusBadRequest,
		}
	}
	pag.Default()
	resp, err := uc.service.GetAllUsers(&pag, role.(string), "super_admin")
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}
	var notification = dto2.NotificationPayload{
		UserId:  userId.(uint),
		Type:    constants.NotificationKybAdded,
		Message: "new kyb application added",
		Role:    role.(string),
	}
	connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationKybAdded, notification)

	notification.Type = constants.NotificationKycAdded
	notification.Message = "new kyc application added"
	connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationKycAdded, notification)

	notification.Type = constants.NotificationTransactionAdded
	notification.Message = "new transaction added"
	connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationTransactionAdded, notification)

	notification.Type = constants.NotificationAuthChangedPassword
	notification.Message = "password changed successfully"
	connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationAuthChangedPassword, notification)

	return Ok(ctx, resp)
}

func (uc *UserController) ChangePassword(ctx *gin.Context) *AppResp {
	userId, _ := ctx.Get("user_id")
	user, err := uc.service.GetById(userId.(uint))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "user not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed to get user",
			Code:    http.StatusInternalServerError,
		}
	}
	var input dto2.ChangePasswordDto
	if err = ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid input",
			Code:    http.StatusBadRequest,
		}
	}
	if err = input.Validate(user.Password); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "validation error",
			Code:    http.StatusBadRequest,
		}
	}
	if err = uc.service.ChangePassword(user, &input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "failed to change password",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "password successfully changed")
}

func (uc *UserController) ResetPassword(ctx *gin.Context) *AppResp {
	userId := ctx.Param("user_id")
	user, err := uc.service.GetById(parsers.ParamUint(userId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "user not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}

	if user.Role == constants.AdminRole {
		return &AppResp{
			Error:   "forbidden",
			Message: "forbidden",
			Code:    http.StatusForbidden,
		}
	}
	var inputDto dto2.ResetPasswordDto
	if err = ctx.ShouldBindJSON(&inputDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid input",
			Code:    http.StatusBadRequest,
		}
	}
	if err = inputDto.Validate(); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		}
	}

	role, _ := ctx.Get("role")
	if user.Role == role.(string) {
		return &AppResp{
			Error:   "denied",
			Message: "denied",
			Code:    http.StatusForbidden,
		}
	}

	if err = uc.service.ResetPassword(user, &inputDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "failed",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "password successfully reset")
}

func (uc *UserController) GetAllAdmins(ctx *gin.Context) *AppResp {
	users, err := uc.service.GetAllAdmins()
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, users)
}
