package services

import (
	"auth-service/core/config"
	"auth-service/core/connect"
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/models"
	"auth-service/internal/repositories"
	"auth-service/pkg/security"
	"auth-service/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"time"
)

type CompanyServiceInterface interface {
	VerifyEmail(code string) (string, string, error)
	CreateCompany(companyRequest *dto.CompanyRequest) error
	ProfileLogin(email, password string) (string, string, error)
	RefreshTokens(refreshToken string) (string, string, error)
	DeleteCompany(companyID uint) error
	GetCompany(companyID uint) (*models.Company, error)
	GetCompanies(status string, page, limit int) ([]*models.Company, int64, error)
	InviteUser(companyID uint, request *dto.InviteData) (string, error)
	LogOut(email string, accessToken string) error
	GetAllProfilesByCompanyId(companyID uint) ([]models.Profile, error)
}

type CompanyService struct {
	repo        repositories.CompanyRepositoryInterface
	profileRepo repositories.ProfileRepositoryInterface
}

func NewCompanyService(repo repositories.CompanyRepositoryInterface, profileRepo repositories.ProfileRepositoryInterface) *CompanyService {
	return &CompanyService{
		repo:        repo,
		profileRepo: profileRepo,
	}
}

func (cs *CompanyService) CreateCompany(companyRequest *dto.CompanyRequest) error {
	ctx := context.Background()

	exists, err := cs.repo.CheckCompanyByEmail(companyRequest.Email)
	if err != nil {
		return fmt.Errorf("ошибка при проверке email: %w", err)
	}
	if exists {
		return fmt.Errorf("компания с таким email уже зарегистрирована")
	}

	pattern := "company:verify:*"
	iter := connect.Redis.Scan(ctx, 0, pattern, 0).Iterator()
	for iter.Next(ctx) {
		val, err := connect.Redis.Get(ctx, iter.Val()).Result()
		if err == nil && val != "" {
			var cmp models.Company
			if err := json.Unmarshal([]byte(val), &cmp); err == nil && cmp.Email == companyRequest.Email {
				return fmt.Errorf("письмо с подтверждением уже отправлено. Подождите или проверьте email")
			}
		}
	}
	if err := iter.Err(); err != nil {
		return fmt.Errorf("ошибка при сканировании redis: %w", err)
	}

	verificationCode := "cf1e8e19-8a10-4a7c-bb3e-4b50ecf9a3a2"

	hashedPassword, err := security.Hash(companyRequest.Password)
	if err != nil {
		return fmt.Errorf("не удалось захешировать пароль: %w", err)
	}

	tempCompany := models.Company{
		CompanyTradingName: companyRequest.CompanyTradingName,
		Country:            companyRequest.Country,
		ContactPhone:       companyRequest.ContactPhone,
		Email:              companyRequest.Email,
		Password:           string(hashedPassword),
		SelectedProduct:    constants.ProductType(companyRequest.SelectedProduct),
		EmailVerified:      false,
	}

	companyBytes, err := json.Marshal(tempCompany)
	if err != nil {
		return fmt.Errorf("не удалось сериализовать компанию: %w", err)
	}

	key := fmt.Sprintf("company:verify:%s", verificationCode)
	err = connect.Redis.Set(ctx, key, companyBytes, 5*time.Minute).Err()
	if err != nil {
		return fmt.Errorf("ошибка при сохранении кода в redis: %w", err)
	}

	// err = utils.SendVerificationCodeToEmail(companyRequest.Email, verificationCode)
	// if err != nil {
	// 	return fmt.Errorf("не удалось отправить письмо: %w", err)
	// }

	return nil
}

func (cs *CompanyService) VerifyEmail(code string) (string, string, error) {
	ctx := context.Background()

	key := fmt.Sprintf("company:verify:%s", code)

	val, err := connect.Redis.Get(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return "", "", fmt.Errorf("код подтверждения недействителен или истёк")
	} else if err != nil {
		return "", "", err
	}

	var company models.Company
	err = json.Unmarshal([]byte(val), &company)
	if err != nil {
		return "", "", err
	}

	company.EmailVerified = true

	profile := &models.Profile{
		Email:    company.Email,
		Role:     constants.CompanyOwner,
		Password: company.Password,
	}

	err = cs.repo.CreateCompanyWithOwner(&company, profile)
	if err != nil {
		return "", "", err
	}

	accessToken, refreshToken, err := security.GenerateTokens(profile.ID, company.Email, "", nil)
	if err != nil {
		return "", "", fmt.Errorf("ошибка генерации токенов: %w", err)

	}

	refreshKey := fmt.Sprintf("refresh_token:%s", company.Email)
	if err := connect.Redis.Set(ctx, refreshKey, refreshToken, 7*24*time.Hour).Err(); err != nil {
		return "", "", fmt.Errorf("ошибка сохранения токена: %w", err)

	}

	connect.Redis.Del(ctx, key)

	return accessToken, refreshToken, nil
}

func (cs *CompanyService) ProfileLogin(email, password string) (string, string, error) {
	profile, err := cs.profileRepo.GetProfileByEmail(email)
	if err != nil {
		return "", "", fmt.Errorf("profile with such email does not exist")
	}

	if err := security.VerifyPassword(profile.Password, password); err != nil {
		return "", "", fmt.Errorf("incorrect password")
	}

	permissions, err := cs.profileRepo.GetPermissionsByProfileID(profile.ID)
	if err != nil {
		return "", "", fmt.Errorf("failed to get permissions: %v", err)
	}

	permMap := make(map[uint][]string)
	for _, p := range permissions {
		permMap[p.AccountID] = append(permMap[p.AccountID], p.Permission)
	}

	access, refresh, err := security.GenerateTokens(profile.ID, profile.Email, string(profile.Role), permMap)
	if err != nil {
		return "", "", err
	}

	ctx := context.Background()
	redisKey := fmt.Sprintf("refresh_token:%s", profile.Email)
	connect.Redis.Del(ctx, redisKey)
	if err := connect.Redis.Set(ctx, redisKey, refresh, security.RefreshExpiry).Err(); err != nil {
		return "", "", err
	}

	return access, refresh, nil
}

func (cs *CompanyService) RefreshTokens(refreshToken string) (string, string, error) {
	ctx := context.Background()

	// Проверка refresh токена
	claims, err := security.ValidateToken2(refreshToken, security.RefreshSecret, "refresh")
	if err != nil {
		return "", "", err
	}

	// Проверка токена в Redis
	storedToken, err := connect.Redis.Get(ctx, fmt.Sprintf("refresh_token:%s", claims.Email)).Result()
	if err != nil || storedToken != refreshToken {
		return "", "", fmt.Errorf("refresh_token not found or expired")
	}

	// Получаем профиль пользователя
	profile, err := cs.profileRepo.GetProfileByEmail(claims.Email)
	if err != nil {
		return "", "", fmt.Errorf("пользователь не найден: %v", err)
	}

	// Получаем permissions пользователя
	permissions, err := cs.profileRepo.GetPermissionsByProfileID(profile.ID)
	if err != nil {
		return "", "", fmt.Errorf("не удалось получить права доступа: %v", err)
	}

	permMap := make(map[uint][]string)
	for _, p := range permissions {
		permMap[p.AccountID] = append(permMap[p.AccountID], p.Permission)
	}

	// Генерация новых токенов с нужными данными
	newAccess, newRefresh, err := security.GenerateTokens(profile.ID, claims.Email, string(profile.Role), permMap)
	if err != nil {
		return "", "", fmt.Errorf("ошибка генерации токенов: %w", err)
	}

	// Обновляем токен в Redis
	redisKey := fmt.Sprintf("refresh_token:%s", claims.Email)
	connect.Redis.Del(ctx, redisKey)
	err = connect.Redis.Set(ctx, redisKey, newRefresh, 7*24*time.Hour).Err()
	if err != nil {
		return "", "", fmt.Errorf("ошибка сохранения refresh_token: %w", err)
	}

	return newAccess, newRefresh, nil
}

func (cs *CompanyService) DeleteCompany(companyID uint) error {
	err := cs.repo.DeleteCompany(companyID)
	if err != nil {
		return err
	}

	return nil
}

func (cs *CompanyService) GetCompany(companyID uint) (*models.Company, error) {
	company, err := cs.repo.GetCompany(companyID)
	if err != nil {
		return nil, err
	}

	return company, nil
}

func (cs *CompanyService) GetCompanies(status string, page, limit int) ([]*models.Company, int64, error) {
	return cs.repo.GetCompanies(status, page, limit)

}

func (cs *CompanyService) InviteUser(companyID uint, request *dto.InviteData) (string, error) {
	// 1. Проверяем, существует ли компания
	_, err := cs.repo.GetCompany(companyID)
	if err != nil {
		return "", errors.New("company not found")
	}

	// 2. Проверяем, что пользователя с таким email ещё нет
	if _, err := cs.profileRepo.GetProfileByEmail(request.Email); err == nil {
		return "", errors.New("user with this email already exists")
	}

	// 3. Генерируем уникальный токен (UUID)
	token := uuid.New().String()
	ctx := context.Background()

	// 4. Создаём структуру, которую сохраним в Redis
	inviteData := dto.InviteData{
		CompanyID:          companyID,
		FirstName:          request.FirstName,
		LastName:           request.LastName,
		Email:              request.Email,
		Role:               constants.CompanyRole(request.Role),
		AccountPermissions: request.AccountPermissions,
	}

	// Сериализуем в JSON
	payload, err := json.Marshal(inviteData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal invite data: %v", err)
	}

	// 5. Сохраняем JSON в Redis с TTL 15 минут
	redisKey := fmt.Sprintf("invite:%s", token)
	if err := connect.Redis.Set(ctx, redisKey, payload, 15*time.Minute).Err(); err != nil {
		return "", fmt.Errorf("failed to save invite in redis: %v", err)
	}

	conf := config.Get()
	inviteLink := fmt.Sprintf("%s/confirm-password?token=%s", conf.Server.FrontUrl, token)

	// 7. Рендерим шаблон email (invite_email.html)
	htmlContent, err := utils.RenderTemplate("invite_email.html", map[string]string{
		"InviteLink": inviteLink,
		"FirstName":  request.FirstName,
	})
	if err != nil {
		return "", fmt.Errorf("ошибка при рендеринге шаблона письма: %w", err)
	}

	// 8. Отправляем email через SendGrid
	emailS := utils.Email{
		SenderName:       conf.SendGrid.SenderName,
		SenderEmail:      conf.SendGrid.SenderEmail,
		RecipientEmail:   request.Email,
		Subject:          "Приглашение в компанию",
		HtmlContent:      htmlContent,
		PlainTextContent: fmt.Sprintf("Здравствуйте, %s!\n\nЧтобы присоединиться к компании, перейдите по ссылке: %s\nСсылка действительна 15 минут.", request.FirstName, inviteLink),
	}
	if err := utils.SendEmail(emailS); err != nil {
		return "", fmt.Errorf("ошибка при отправке письма: %v", err)
	}

	return token, nil
}

func (cs *CompanyService) LogOut(email string, accessToken string) error {
	ctx := context.Background()

	err := cs.repo.DeleteToken(email)
	if err != nil {
		return err
	}

	if accessToken != "" {
		ttl := security.GetTTLFromToken(accessToken)
		blacklistKey := fmt.Sprintf("blacklist_token:%s", accessToken)
		_ = connect.Redis.Set(ctx, blacklistKey, "1", ttl).Err()
	}

	return nil
}

func (cs *CompanyService) GetAllProfilesByCompanyId(companyID uint) ([]models.Profile, error) {
	return cs.profileRepo.GetAllProfilesByCompanyId(companyID)
}
