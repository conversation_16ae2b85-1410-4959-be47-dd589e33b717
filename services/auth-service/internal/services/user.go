package services

import (
	"auth-service/core/config"
	dto2 "auth-service/internal/dto"
	"auth-service/internal/integrations/rest"
	"auth-service/internal/models"
	"auth-service/internal/repositories"
	security2 "auth-service/pkg/security"
	"errors"
	_ "fmt"

	"gorm.io/gorm"
)

type UserServiceInterface interface {
	LoginService(input models.User) (map[string]interface{}, error)
	SetToken(username, token string) error
	GetToken(username string) (string, error)
	DeleteToken(username string) error
	GetByUsername(username string) (*models.User, error)
	GetById(id uint) (*models.User, error)
	ChangePassword(user *models.User, dto *dto2.ChangePasswordDto) error
	CreateUser(input *dto2.CreateUserDto) (*models.User, error)
	ResetPassword(user *models.User, dto *dto2.ResetPasswordDto) error
	GetAllUsers(pag *dto2.PaginationDto, excludeRoles ...string) (*dto2.AllUserRespDto, error)
	GetAllAdmins() ([]dto2.AdminDto, error)
	UpdateUser(user *models.User, dto dto2.UpdateUserDto) error
	DeleteUser(userID uint, executorUserId uint) error
}

type UserService struct {
	repo repositories.UserRepositoryInterface
}

func NewUserService(repo repositories.UserRepositoryInterface) *UserService {
	return &UserService{
		repo: repo,
	}
}

func (us *UserService) DeleteUser(userID uint, executorUserId uint) error {
	err := us.repo.WithTransaction(func(tx *gorm.DB) error {
		var user models.User
		var executorUser models.User
		if err := us.repo.FindById(&user, userID, tx); err != nil {
			return err
		}
		if err := us.repo.FindById(&executorUser, executorUserId, tx); err != nil {
			return err
		}
		if err := us.repo.DeleteUser(userID, tx); err != nil {
			return err
		}

		token, err := us.GetToken(executorUser.Username)
		if err != nil {
			return err
		}

		if err = rest.DeleteUserFromTransactionService(token, userID); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (us *UserService) UpdateUser(user *models.User, input dto2.UpdateUserDto) error {
	if input.Username != nil {
		user.Username = *input.Username
	}
	if input.Name != nil {
		user.Name = *input.Name
	}
	if input.LastName != nil {
		user.LastName = *input.LastName
	}
	if input.Email != nil {
		user.Email = *input.Email
	}
	if input.Role != nil {
		user.Role = *input.Role
	}

	if err := us.repo.UpdateUser(user); err != nil {
		return err
	}

	return nil
}

func (us *UserService) GetAllAdmins() ([]dto2.AdminDto, error) {
	admins, err := us.repo.GetAllAdmins()
	if err != nil {
		return nil, err
	}
	var adminsResp []dto2.AdminDto
	for _, v := range admins {
		var adminDto = dto2.AdminDto{
			UserId: v.ID,
			Role:   v.Role,
		}
		adminsResp = append(adminsResp, adminDto)
	}
	return adminsResp, nil
}

func (us *UserService) CreateUser(input *dto2.CreateUserDto) (*models.User, error) {
	hashedPassword, err := security2.Hash(input.Password)
	if err != nil {
		return nil, err
	}
	var user = models.User{
		Username: input.Username,
		Password: string(hashedPassword),
		Name:     input.Name,
		LastName: input.LastName,
		Email:    input.Email,
		Role:     input.Role,
	}

	if err = us.repo.CreateUser(&user); err != nil {
		return nil, err
	}
	user.Password = ""
	return &user, nil
}

func (us *UserService) LoginService(input models.User) (map[string]interface{}, error) {
	password := input.Password

	if err := us.repo.FindUserByUsername(&input, input.Username); err != nil {
		return nil, errors.New("user not found")
	}

	if err := security2.VerifyPassword(input.Password, password); err != nil {
		return nil, errors.New("invalid credentials")
	}

	token, err := security2.GenerateToken(input.Username, input.ID, input.Role, config.Get().Token.SecretKey)
	if err != nil {
		return nil, err
	}

	if err = us.repo.SetToken(input.Username, token); err != nil {
		return nil, err
	}

	userData := make(map[string]interface{})

	userData["Token"] = token
	userData["Id"] = input.ID
	userData["Username"] = input.Username
	userData["Role"] = input.Role
	return userData, nil
}

func (us *UserService) GetByUsername(username string) (*models.User, error) {
	var user models.User

	if err := us.repo.FindUserByUsername(&user, username); err != nil {
		return nil, err
	}

	return &user, nil
}

func (us *UserService) ChangePassword(user *models.User, dto *dto2.ChangePasswordDto) error {
	newHashedPassword, err := security2.Hash(dto.NewPassword)
	if err != nil {
		return err
	}
	user.Password = string(newHashedPassword)
	if err = us.repo.UpdateUser(user); err != nil {
		return err
	}
	return nil
}

func (us *UserService) ResetPassword(user *models.User, dto *dto2.ResetPasswordDto) error {
	hashedPassword, err := security2.Hash(dto.NewPassword)
	if err != nil {
		return errors.New("failed to create password")
	}
	user.Password = string(hashedPassword)
	if err = us.repo.UpdateUser(user); err != nil {
		return err
	}
	return nil
}

func (us *UserService) GetById(id uint) (*models.User, error) {
	var user models.User
	if err := us.repo.FindById(&user, id, nil); err != nil {
		return nil, err
	}
	return &user, nil
}

func (us *UserService) GetAllUsers(pag *dto2.PaginationDto, excludeRoles ...string) (*dto2.AllUserRespDto, error) {
	users, count, err := us.repo.GetAllUsers(pag, excludeRoles...)
	if err != nil {
		return nil, err
	}
	var allUsersRespDto dto2.AllUserRespDto
	allUsersRespDto.Count = count
	for _, v := range users {
		user := dto2.UsersRespDto{
			ID:       v.ID,
			Username: v.Username,
			Role:     v.Role,
			Name:     v.Name,
			LastName: v.LastName,
			Email:    v.Email,
		}
		allUsersRespDto.Users = append(allUsersRespDto.Users, user)
	}
	return &allUsersRespDto, nil
}

func (us *UserService) SetToken(username, token string) error {
	return us.repo.SetToken(username, token)
}

func (us *UserService) GetToken(username string) (string, error) {
	return us.repo.GetToken(username)
}

func (us *UserService) DeleteToken(username string) error {
	return us.repo.DeleteToken(username)
}
