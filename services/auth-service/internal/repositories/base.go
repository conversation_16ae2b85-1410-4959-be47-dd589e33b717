package repositories

import (
	"errors"

	"gorm.io/gorm"
)

type BaseRepositoryInterface interface {
	WithTransaction(fc func(tx *gorm.DB) error) error
}

type BaseRepository struct {
	db *gorm.DB
}

func (br *BaseRepository) WithTransaction(fc func(tx *gorm.DB) error) error {
	if br.db == nil {
		return errors.New("database connection is not initialized")
	}
	if fc == nil {
		return errors.New("transaction function is not provided")
	}
	return br.db.Transaction(fc)
}

func (br *BaseRepository) GetDB(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return br.db
	}

	return tx
}