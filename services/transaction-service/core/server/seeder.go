package server

import (
	"log"
	seeds "transaction-service/internal/seeds"
	"github.com/elliotchance/orderedmap"
)

func Runner(sources []string) {
	log.Println("Seeding...")

	router := orderedmap.NewOrderedMap()
	//router.Set("bank", seeds.NewBankSeed().Seed)
	//router.Set("currency", seeds.NewCurrencySeed().Seed)
	//router.Set("currency_exchange", seeds.NewCurrencyExchangeSeed().Seed)
	router.Set("fee_category", seeds.NewFeeCategorySeed().Seed)
	router.Set("fee_type", seeds.NewFeeTypeSeed().Seed)

	if len(sources) != 0 {
		for _, source := range sources {
			single_seed, exists := router.Get(source)
			if !(exists) {
				log.Printf("No source registered with the name: %s", source)
				continue
			}
			runSeed(single_seed)
		}
	} else {
		for el := router.Front(); el != nil; el = el.Next() {
			runSeed(el.Value)
		}
	}

	log.Println("Finish seeding")
}

func runSeed(fn interface{}) {
	call := fn.(func() (seeds.Summary, error))
	call()
}
