package http

import (
	"net/http"
	"transaction-service/core/connect"
)

// SelectiveHTTPClient wraps the real HTTP client and allows selective mocking
type SelectiveHTTPClient struct {
	realClient connect.HTTPClientInterface
	mockClient connect.HTTPClientInterface
	useMock    bool
}

// NewSelectiveHTTPClient creates a new selective HTTP client
func NewSelectiveHTTPClient(realClient connect.HTTPClientInterface) *SelectiveHTTPClient {
	return &SelectiveHTTPClient{
		realClient: realClient,
		useMock:    false,
	}
}

// Do implements the HTTPClient interface
func (s *SelectiveHTTPClient) Do(req *http.Request) (*http.Response, error) {
	if s.useMock && s.mockClient != nil {
		return s.mockClient.Do(req)
	}
	return s.realClient.Do(req)
}

// SetMockClient sets the mock client and enables mocking
func (s *SelectiveHTTPClient) SetMockClient(mockClient connect.HTTPClientInterface) {
	s.mockClient = mockClient
	s.useMock = true
}

// DisableMock disables mocking and uses the real client
func (s *SelectiveHTTPClient) DisableMock() {
	s.useMock = false
}

// EnableMock enables mocking (if mock client is set)
func (s *SelectiveHTTPClient) EnableMock() {
	if s.mockClient != nil {
		s.useMock = true
	}
}

// IsMockEnabled returns whether mocking is currently enabled
func (s *SelectiveHTTPClient) IsMockEnabled() bool {
	return s.useMock && s.mockClient != nil
}

// GetRealClient returns the underlying real HTTP client
func (s *SelectiveHTTPClient) GetRealClient() connect.HTTPClientInterface {
	return s.realClient
}

// GetMockClient returns the current mock client
func (s *SelectiveHTTPClient) GetMockClient() connect.HTTPClientInterface {
	return s.mockClient
}

// Global functions to work with the centralized HTTP client

// Do makes an HTTP request using the centralized HTTP client
func Do(req *http.Request) (*http.Response, error) {
	if connect.HTTPClient == nil {
		// Fallback to default client if not initialized
		connect.HTTPClient = &http.Client{}
	}
	return connect.HTTPClient.Do(req)
}

// SetMockClient sets up selective mocking for the centralized HTTP client
func SetMockClient(mockClient connect.HTTPClientInterface) {
	if selectiveClient, ok := connect.HTTPClient.(*SelectiveHTTPClient); ok {
		selectiveClient.SetMockClient(mockClient)
	} else {
		// Wrap the existing client in a selective client
		selectiveClient := NewSelectiveHTTPClient(connect.HTTPClient)
		selectiveClient.SetMockClient(mockClient)
		connect.HTTPClient = selectiveClient
	}
}

// DisableMock disables mocking for the centralized HTTP client
func DisableMock() {
	if selectiveClient, ok := connect.HTTPClient.(*SelectiveHTTPClient); ok {
		selectiveClient.DisableMock()
	}
}

// EnableMock enables mocking for the centralized HTTP client
func EnableMock() {
	if selectiveClient, ok := connect.HTTPClient.(*SelectiveHTTPClient); ok {
		selectiveClient.EnableMock()
	}
}

// IsMockEnabled returns whether mocking is enabled for the centralized HTTP client
func IsMockEnabled() bool {
	if selectiveClient, ok := connect.HTTPClient.(*SelectiveHTTPClient); ok {
		return selectiveClient.IsMockEnabled()
	}
	return false
}
