package connect

import (
	"net/http"
	"transaction-service/core/rabbitmq"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HTTPClientInterface for making HTTP requests
type HTTPClientInterface interface {
	Do(req *http.Request) (*http.Response, error)
}

var DB *gorm.DB
var Redis *redis.Client
var Logger *zap.Logger
var RabbitMQ *rabbitmq.RabbitMQ
var Router *gin.Engine
var HTTPClient HTTPClientInterface // Centralized HTTP client
var TestRestMock interface{}       // For storing test mock client
