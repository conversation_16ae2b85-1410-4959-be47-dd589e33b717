package helpers

import (
	"fmt"
	"transaction-service/core/server"
	"transaction-service/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TestEnvironment holds all test utilities and dependencies
type TestEnvironment struct {
	Router         *gin.Engine
	DB             *gorm.DB
	AccountManager *TestAccountManager
	DatabaseHelper *DatabaseHelper
	Factory        *TestDataFactory
	Services       *TestServices
}

// TestServices holds references to all services for testing
type TestServices struct {
	AccountService services.AccountServiceInterface
	// Add other services as needed
}

// SetupTestEnvironment initializes the complete test environment
func SetupTestEnvironment() (*TestEnvironment, error) {
	// Initialize test server and router
	s := &server.Server{Test: true}
	router, _, err := s.InitTest()
	if err != nil {
		return nil, fmt.Errorf("failed to initialize test server: %w", err)
	}

	// Get database connection from the server
	// Note: This assumes the server exposes the database connection
	// You might need to modify this based on your server implementation
	db, err := getDBFromServer(s)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// Get services from the server
	services, err := getServicesFromServer(s)
	if err != nil {
		return nil, fmt.Errorf("failed to get services: %w", err)
	}

	// Initialize helpers
	dbHelper := NewDatabaseHelper(db)
	factory := NewTestDataFactory()
	accountManager := NewTestAccountManager(services.AccountService, db)

	return &TestEnvironment{
		Router:         router,
		DB:             db,
		AccountManager: accountManager,
		DatabaseHelper: dbHelper,
		Factory:        factory,
		Services:       services,
	}, nil
}

// Cleanup performs cleanup operations for the test environment
func (te *TestEnvironment) Cleanup() error {
	if te.AccountManager != nil {
		return te.AccountManager.CleanupCreatedAccounts()
	}
	return nil
}

// CleanupTestUser removes all data for a specific test user
func (te *TestEnvironment) CleanupTestUser(userId uint) error {
	if te.DatabaseHelper != nil {
		return te.DatabaseHelper.CleanTestUserData(userId)
	}
	return nil
}

// Reset resets the test environment for a new test
func (te *TestEnvironment) Reset() {
	if te.AccountManager != nil {
		te.AccountManager.Reset()
	}
}

// GetAccountStats returns statistics about accounts in the test environment
func (te *TestEnvironment) GetAccountStats() (*AccountStats, error) {
	if te.DatabaseHelper == nil {
		return nil, fmt.Errorf("database helper not initialized")
	}

	totalAccounts, err := te.DatabaseHelper.GetAccountCount()
	if err != nil {
		return nil, err
	}

	createdAccounts := 0
	if te.AccountManager != nil {
		createdAccounts = te.AccountManager.GetCreatedAccountsCount()
	}

	return &AccountStats{
		TotalAccounts:   totalAccounts,
		CreatedAccounts: int64(createdAccounts),
	}, nil
}

// AccountStats holds statistics about accounts
type AccountStats struct {
	TotalAccounts   int64
	CreatedAccounts int64
}

// TestCase represents a test case with setup and cleanup
type TestCase struct {
	Name        string
	Environment *TestEnvironment
	Setup       func(*TestEnvironment) error
	Cleanup     func(*TestEnvironment) error
}

// Run executes a test case with proper setup and cleanup
func (tc *TestCase) Run(testFunc func(*TestEnvironment) error) error {
	// Setup
	if tc.Setup != nil {
		if err := tc.Setup(tc.Environment); err != nil {
			return fmt.Errorf("test setup failed: %w", err)
		}
	}

	// Defer cleanup
	defer func() {
		if tc.Cleanup != nil {
			tc.Cleanup(tc.Environment)
		}
	}()

	// Run test
	return testFunc(tc.Environment)
}

// Helper functions to extract dependencies from server
// Note: These functions might need to be adjusted based on your server implementation

func getDBFromServer(s *server.Server) (*gorm.DB, error) {
	// This is a placeholder - you'll need to implement this based on how
	// your server exposes the database connection
	// For now, we'll return an error indicating this needs implementation
	return nil, fmt.Errorf("getDBFromServer needs implementation - please expose DB from server")
}

func getServicesFromServer(s *server.Server) (*TestServices, error) {
	// This is a placeholder - you'll need to implement this based on how
	// your server exposes the services
	// For now, we'll return an error indicating this needs implementation
	return nil, fmt.Errorf("getServicesFromServer needs implementation - please expose services from server")
}

// Alternative setup function that works with existing test structure
func SetupTestEnvironmentSimple() (*gin.Engine, *TestAccountManager, *DatabaseHelper, *TestDataFactory, error) {
	// Initialize test server and router using existing pattern
	s := &server.Server{Test: true}
	router, _, err := s.InitTest()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to initialize test server: %w", err)
	}

	// For now, return the router and nil helpers until server is modified to expose dependencies
	factory := NewTestDataFactory()
	
	return router, nil, nil, factory, nil
}
