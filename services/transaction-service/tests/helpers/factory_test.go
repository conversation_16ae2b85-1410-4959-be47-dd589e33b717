package helpers

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestTestDataFactory tests the test data factory functionality
func TestTestDataFactory(t *testing.T) {
	factory := NewTestDataFactory()

	t.Run("CreateMinimalAccountDto", func(t *testing.T) {
		dto := factory.CreateMinimalAccountDto(1, 2, "Test Account")
		
		assert.Equal(t, uint(1), dto.OwnerId)
		assert.Equal(t, uint(2), dto.CurrencyId)
		assert.Equal(t, "Test Account", dto.Name)
	})

	t.Run("CreateAccountDtoWithAmount", func(t *testing.T) {
		dto := factory.CreateAccountDtoWithAmount(1, 2, "Test Account", 100.50)
		
		assert.Equal(t, uint(1), dto.OwnerId)
		assert.Equal(t, uint(2), dto.CurrencyId)
		assert.Equal(t, "Test Account", dto.Name)
		assert.Equal(t, "digital", dto.AssetType)
		assert.Equal(t, "active", dto.Status)
		assert.True(t, dto.CurrentAmount.Equal(decimal.NewFromFloat(100.50)))
	})

	t.Run("CreateZeroBalanceAccountDto", func(t *testing.T) {
		dto := factory.CreateZeroBalanceAccountDto(1, 2, "Zero Account")
		
		assert.Equal(t, uint(1), dto.OwnerId)
		assert.Equal(t, uint(2), dto.CurrencyId)
		assert.Equal(t, "Zero Account", dto.Name)
		assert.True(t, dto.CurrentAmount.IsZero())
	})

	t.Run("CreateTestAccountName", func(t *testing.T) {
		name := factory.CreateTestAccountName("Account", 5)
		assert.Equal(t, "Account 5", name)
	})

	t.Run("GetTestCurrencies", func(t *testing.T) {
		currencies := factory.GetTestCurrencies()
		assert.Len(t, currencies, 6)
		assert.Contains(t, currencies, TestCurrencyUSD)
		assert.Contains(t, currencies, TestCurrencyEUR)
		assert.Contains(t, currencies, TestCurrencyGBP)
	})

	t.Run("CreateMultipleAccountDtos", func(t *testing.T) {
		dtos := factory.CreateMultipleAccountDtos(1, 2, 3, "Multi Account")
		
		assert.Len(t, dtos, 3)
		for i, dto := range dtos {
			assert.Equal(t, uint(1), dto.OwnerId)
			assert.Equal(t, uint(2), dto.CurrencyId)
			assert.Equal(t, factory.CreateTestAccountName("Multi Account", i+1), dto.Name)
			expectedAmount := decimal.NewFromFloat(100.0 * float64(i+1))
			assert.True(t, dto.CurrentAmount.Equal(expectedAmount))
		}
	})

	t.Run("TestConstants", func(t *testing.T) {
		assert.Equal(t, uint(1), TestUserId1)
		assert.Equal(t, uint(2), TestUserId2)
		assert.Equal(t, uint(1), TestCurrencyUSD)
		assert.Equal(t, uint(2), TestCurrencyEUR)
		assert.Equal(t, uint(3), TestCurrencyGBP)
		assert.Equal(t, uint(4), TestCurrencyJPY)
		assert.Equal(t, uint(5), TestCurrencyRUB)
		assert.Equal(t, uint(6), TestCurrencyCNY)
	})
}
