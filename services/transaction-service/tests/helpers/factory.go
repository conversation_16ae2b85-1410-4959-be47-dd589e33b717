package helpers

import (
	"fmt"
	"transaction-service/internal/dto"

	"github.com/shopspring/decimal"
)

// TestDataFactory provides factory methods for creating test data
type TestDataFactory struct{}

// NewTestDataFactory creates a new test data factory
func NewTestDataFactory() *TestDataFactory {
	return &TestDataFactory{}
}

// CreateMinimalAccountDto creates a minimal account DTO for testing
func (tdf *TestDataFactory) CreateMinimalAccountDto(ownerId, currencyId uint, name string) *dto.MinimalAccountCreateDto {
	return &dto.MinimalAccountCreateDto{
		OwnerId:    ownerId,
		CurrencyId: currencyId,
		Name:       name,
	}
}

// CreateAccountDto creates a full account DTO for testing
func (tdf *TestDataFactory) CreateAccountDto(ownerId, currencyId uint, name, assetType string, amount decimal.Decimal) *dto.AccountCreateDto {
	return &dto.AccountCreateDto{
		OwnerId:       ownerId,
		CurrencyId:    currencyId,
		Name:          name,
		AssetType:     assetType,
		Status:        "active",
		CurrentAmount: amount,
	}
}

// CreateDigitalAccountDto creates a digital account DTO for testing
func (tdf *TestDataFactory) CreateDigitalAccountDto(ownerId, currencyId uint, name string, amount decimal.Decimal) *dto.AccountCreateDto {
	return tdf.CreateAccountDto(ownerId, currencyId, name, "digital", amount)
}

// CreateBankAccountDto creates a bank account DTO for testing
func (tdf *TestDataFactory) CreateBankAccountDto(ownerId, currencyId uint, name string, amount decimal.Decimal) *dto.AccountCreateDto {
	return tdf.CreateAccountDto(ownerId, currencyId, name, "bank", amount)
}

// CreateTestAccountName generates a test account name
func (tdf *TestDataFactory) CreateTestAccountName(prefix string, index int) string {
	if prefix == "" {
		prefix = "Test Account"
	}
	return fmt.Sprintf("%s %d", prefix, index)
}

// CreateUniqueTestAccountName generates a unique test account name with timestamp
func (tdf *TestDataFactory) CreateUniqueTestAccountName(prefix string) string {
	if prefix == "" {
		prefix = "Test Account"
	}
	return fmt.Sprintf("%s %d", prefix, GetCurrentTimestamp())
}

// Common test values
const (
	TestUserId1     = uint(1)
	TestUserId2     = uint(2)
	TestCurrencyUSD = uint(1) // Assuming USD has ID 1
	TestCurrencyEUR = uint(2) // Assuming EUR has ID 2
	TestCurrencyGBP = uint(3) // Assuming GBP has ID 3
	TestCurrencyJPY = uint(4) // Assuming JPY has ID 4
	TestCurrencyRUB = uint(5) // Assuming RUB has ID 5
	TestCurrencyCNY = uint(6) // Assuming CNY has ID 6
)

// GetTestCurrencies returns a list of test currency IDs
func (tdf *TestDataFactory) GetTestCurrencies() []uint {
	return []uint{
		TestCurrencyUSD,
		TestCurrencyEUR,
		TestCurrencyGBP,
		TestCurrencyJPY,
		TestCurrencyRUB,
		TestCurrencyCNY,
	}
}

// CreateTestAccountsForCurrencies creates account DTOs for multiple currencies
func (tdf *TestDataFactory) CreateTestAccountsForCurrencies(ownerId uint, currencies []uint, namePrefix string) []*dto.MinimalAccountCreateDto {
	accounts := make([]*dto.MinimalAccountCreateDto, len(currencies))
	for i, currencyId := range currencies {
		name := tdf.CreateTestAccountName(namePrefix, i+1)
		accounts[i] = tdf.CreateMinimalAccountDto(ownerId, currencyId, name)
	}
	return accounts
}

// CreateMultipleAccountDtos creates multiple account DTOs for testing
func (tdf *TestDataFactory) CreateMultipleAccountDtos(ownerId, currencyId uint, count int, namePrefix string) []*dto.AccountCreateDto {
	accounts := make([]*dto.AccountCreateDto, count)
	for i := 0; i < count; i++ {
		name := tdf.CreateTestAccountName(namePrefix, i+1)
		amount := decimal.NewFromFloat(100.0 * float64(i+1))
		accounts[i] = tdf.CreateDigitalAccountDto(ownerId, currencyId, name, amount)
	}
	return accounts
}

// CreateAccountDtoWithAmount creates an account DTO with a specific amount
func (tdf *TestDataFactory) CreateAccountDtoWithAmount(ownerId, currencyId uint, name string, amount float64) *dto.AccountCreateDto {
	return tdf.CreateDigitalAccountDto(ownerId, currencyId, name, decimal.NewFromFloat(amount))
}

// CreateZeroBalanceAccountDto creates an account DTO with zero balance
func (tdf *TestDataFactory) CreateZeroBalanceAccountDto(ownerId, currencyId uint, name string) *dto.AccountCreateDto {
	return tdf.CreateAccountDtoWithAmount(ownerId, currencyId, name, 0.0)
}

// Test scenarios for account creation limits
type AccountLimitTestScenario struct {
	Name           string
	CurrencyId     uint
	MaxAccounts    int
	ShouldSucceed  bool
	ExpectedError  string
}

// GetAccountLimitTestScenarios returns predefined test scenarios for account limits
func (tdf *TestDataFactory) GetAccountLimitTestScenarios() []AccountLimitTestScenario {
	return []AccountLimitTestScenario{
		{
			Name:          "USD_Limit_Test",
			CurrencyId:    TestCurrencyUSD,
			MaxAccounts:   5,
			ShouldSucceed: true,
		},
		{
			Name:          "EUR_Limit_Test",
			CurrencyId:    TestCurrencyEUR,
			MaxAccounts:   5,
			ShouldSucceed: true,
		},
		{
			Name:          "Exceed_Limit_Test",
			CurrencyId:    TestCurrencyUSD,
			MaxAccounts:   6,
			ShouldSucceed: false,
			ExpectedError: "Max accounts limit reached",
		},
	}
}

// Helper function to get current timestamp (simplified)
func GetCurrentTimestamp() int64 {
	// This is a simplified timestamp - in real implementation you might want to use time.Now().Unix()
	// For testing purposes, we'll use a simple counter or random number
	return 1000000 // Placeholder
}
