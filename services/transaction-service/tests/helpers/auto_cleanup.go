package helpers

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// AutoCleanupManager handles automatic cleanup of test-created accounts
type AutoCleanupManager struct {
	db                   *gorm.DB
	initialAccountCounts map[string]int64 // Track initial counts by "userId_currencyId"
	testUserId           uint
	testCurrencies       []uint
}

// NewAutoCleanupManager creates a new auto cleanup manager
func NewAutoCleanupManager(testUserId uint, testCurrencies []uint) (*AutoCleanupManager, error) {
	log.Printf("DEBUG: Creating AutoCleanupManager for user %d with currencies %v", testUserId, testCurrencies)

	// Database connection
	dsn := "host=localhost user=postgres password=fafQWllRTl4nfs dbname=monexa_test_db port=5434 sslmode=disable"
	log.Printf("DEBUG: Connecting to database...")
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	log.Printf("DEBUG: Database connection successful")

	manager := &AutoCleanupManager{
		db:                   db,
		initialAccountCounts: make(map[string]int64),
		testUserId:           testUserId,
		testCurrencies:       testCurrencies,
	}

	// Record initial state
	log.Printf("DEBUG: Recording initial state...")
	err = manager.recordInitialState()
	if err != nil {
		return nil, fmt.Errorf("failed to record initial state: %w", err)
	}
	log.Printf("DEBUG: Initial state recorded successfully")

	return manager, nil
}

// recordInitialState records the current account counts before test execution
func (acm *AutoCleanupManager) recordInitialState() error {
	for _, currencyId := range acm.testCurrencies {
		key := fmt.Sprintf("%d_%d", acm.testUserId, currencyId)

		var count int64
		err := acm.db.Raw("SELECT COUNT(*) FROM accounts WHERE owner_id = ? AND currency_id = ? AND deleted_at IS NULL",
			acm.testUserId, currencyId).Scan(&count).Error
		if err != nil {
			return fmt.Errorf("failed to count accounts for user %d currency %d: %w", acm.testUserId, currencyId, err)
		}

		acm.initialAccountCounts[key] = count
	}

	return nil
}

// CleanupTestCreatedAccounts removes only accounts created during the test
func (acm *AutoCleanupManager) CleanupTestCreatedAccounts() error {
	var totalDeleted int64

	for _, currencyId := range acm.testCurrencies {
		key := fmt.Sprintf("%d_%d", acm.testUserId, currencyId)
		initialCount, exists := acm.initialAccountCounts[key]
		if !exists {
			continue
		}

		// Get current count
		var currentCount int64
		err := acm.db.Raw("SELECT COUNT(*) FROM accounts WHERE owner_id = ? AND currency_id = ? AND deleted_at IS NULL",
			acm.testUserId, currencyId).Scan(&currentCount).Error
		if err != nil {
			log.Printf("Warning: Failed to get current count for user %d currency %d: %v", acm.testUserId, currencyId, err)
			continue
		}

		// Calculate how many to delete
		accountsToDelete := currentCount - initialCount
		if accountsToDelete <= 0 {
			continue
		}

		// Get the IDs of the newest accounts (created during test)
		var accountIds []uint
		err = acm.db.Raw(`
			SELECT id FROM accounts 
			WHERE owner_id = ? AND currency_id = ? AND deleted_at IS NULL 
			ORDER BY created_at DESC 
			LIMIT ?`,
			acm.testUserId, currencyId, accountsToDelete).Scan(&accountIds).Error

		if err != nil {
			log.Printf("Warning: Failed to get account IDs for user %d currency %d: %v", acm.testUserId, currencyId, err)
			continue
		}

		// Delete the accounts
		for _, accountId := range accountIds {
			// First zero the balance to allow deletion
			err = acm.db.Exec("UPDATE accounts SET current_amount = 0 WHERE id = ?", accountId).Error
			if err != nil {
				log.Printf("Warning: Failed to zero balance for account %d: %v", accountId, err)
				continue
			}

			// Delete the account
			err = acm.db.Exec("DELETE FROM accounts WHERE id = ?", accountId).Error
			if err != nil {
				log.Printf("Warning: Failed to delete account %d: %v", accountId, err)
			} else {
				totalDeleted++
			}
		}
	}

	return nil
}

// GetTestCreatedAccountsCount returns the number of accounts created during the test
func (acm *AutoCleanupManager) GetTestCreatedAccountsCount() (int64, error) {
	var totalCreated int64

	for _, currencyId := range acm.testCurrencies {
		key := fmt.Sprintf("%d_%d", acm.testUserId, currencyId)
		initialCount, exists := acm.initialAccountCounts[key]
		if !exists {
			continue
		}

		var currentCount int64
		err := acm.db.Raw("SELECT COUNT(*) FROM accounts WHERE owner_id = ? AND currency_id = ? AND deleted_at IS NULL",
			acm.testUserId, currencyId).Scan(&currentCount).Error
		if err != nil {
			return 0, fmt.Errorf("failed to get current count for user %d currency %d: %w", acm.testUserId, currencyId, err)
		}

		created := currentCount - initialCount
		if created > 0 {
			totalCreated += created
		}
	}

	return totalCreated, nil
}

// Close closes the database connection
func (acm *AutoCleanupManager) Close() error {
	sqlDB, err := acm.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
