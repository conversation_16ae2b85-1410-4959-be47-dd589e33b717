package examples

import (
	"transaction-service/tests/mocks"
	"transaction-service/tests/mocks/helpers"
	"transaction-service/tests/mocks/registry"
)

// ExampleRegistryUsage demonstrates how to use the gin-router-style mock registry
func ExampleRegistryUsage() {
	// Create a new REST client mock
	restMock := mocks.NewRestClientMock()

	// Method 1: Using the fluent registry API (similar to gin router)
	authGroup := restMock.Registry()

	// Configure auth service mocks using fluent API
	authGroup.GetAllAdmins().WithSuccess(helpers.CreateTestAdmins())
	authGroup.Login().WithSuccess(`{"token":"test-token","expires_in":3600}`)
	authGroup.RefreshToken().WithUnauthorized()

	// Method 2: Using the advanced registry with middleware
	mockRegistry := restMock.GetMockRegistry()

	// Create a service group
	authServiceGroup := mockRegistry.Group("auth")

	// Get the adapter for interface compatibility
	adapter := mocks.NewMockHTTPClientAdapter(restMock.GetMockClient())

	// Configure endpoints with middleware (similar to gin)
	authServiceGroup.GET("api/v1/user/profile").
		WithJSON(`{"id":1,"name":"Test User","email":"<EMAIL>"}`).
		Use(registry.WithAuth("required-token"), registry.WithCORS()).
		Apply(adapter)

	authServiceGroup.POST("api/v1/user/update").
		WithStatus(200).
		WithJSON(`{"message":"User updated successfully"}`).
		Use(registry.WithContentType("application/json")).
		Apply(adapter)

	// Error scenarios with middleware
	authServiceGroup.GET("api/v1/admin/users").
		Use(registry.WithForbidden()).
		Apply(adapter)

	authServiceGroup.POST("api/v1/auth/invalid").
		Use(registry.WithBadRequest("Invalid credentials")).
		Apply(adapter)

	// Method 3: Traditional method (backward compatibility)
	restMock.MockFetchUserRolesSuccess(helpers.CreateTestAdmins())
	restMock.MockFetchUserRolesUnauthorized()

	// Setup the mock client
	restMock.Setup()
}

// ExampleAdvancedRegistryUsage shows more advanced registry patterns
func ExampleAdvancedRegistryUsage() {
	restMock := mocks.NewRestClientMock()
	mockRegistry := restMock.GetMockRegistry()
	adapter := mocks.NewMockHTTPClientAdapter(restMock.GetMockClient())

	// Register multiple services (similar to gin router setup)
	authService := mockRegistry.Group("auth")
	userService := mockRegistry.Group("user")
	transactionService := mockRegistry.Group("transaction")

	// Auth service endpoints
	authService.POST("api/v1/auth/login").
		WithJSON(`{"token":"jwt-token","user_id":1}`).
		Use(registry.WithContentType("application/json")).
		Apply(adapter)

	authService.POST("api/v1/auth/logout").
		Use(registry.WithNoContent()).
		Apply(adapter)

	// User service endpoints
	userService.GET("api/v1/user/profile").
		WithJSON(`{"id":1,"name":"John Doe","role":"admin"}`).
		Use(registry.WithAuth("bearer-token")).
		Apply(adapter)

	userService.PUT("api/v1/user/profile").
		Use(registry.WithCreated(1)).
		Apply(adapter)

	// Transaction service endpoints
	transactionService.GET("api/v1/transactions").
		WithJSON(`[{"id":1,"amount":100.50,"status":"completed"}]`).
		Use(registry.WithAuth("bearer-token"), registry.WithCORS()).
		Apply(adapter)

	transactionService.POST("api/v1/transactions").
		Use(registry.WithCreated(123)).
		Apply(adapter)

	// Error handling examples
	transactionService.GET("api/v1/transactions/999").
		Use(registry.WithNotFound()).
		Apply(adapter)

	transactionService.DELETE("api/v1/transactions/1").
		Use(registry.WithForbidden()).
		Apply(adapter)

	// Setup all mocks
	restMock.Setup()
}

// ExampleConditionalMocking shows conditional mock responses
func ExampleConditionalMocking() {
	restMock := mocks.NewRestClientMock()
	mockRegistry := restMock.GetMockRegistry()
	adapter := mocks.NewMockHTTPClientAdapter(restMock.GetMockClient())

	authService := mockRegistry.Group("auth")

	// Conditional response based on some test state
	isTestMode := true

	authService.GET("api/v1/user/permissions").
		Use(registry.WithConditionalResponse(
			func() bool { return isTestMode },
			registry.WithJSON(`{"permissions":["read","write","admin"]}`),
			registry.WithUnauthorized(),
		)).
		Apply(adapter)

	// Rate limiting example
	authService.GET("api/v1/user/data").
		WithJSON(`{"data":"user data"}`).
		Use(registry.WithRateLimit(100)).
		Apply(adapter)

	restMock.Setup()
}

// ExampleMockScenarios demonstrates different testing scenarios
func ExampleMockScenarios() {
	restMock := mocks.NewRestClientMock()

	// Scenario 1: Success case
	restMock.Registry().GetAllAdmins().WithSuccess(helpers.CreateTestAdmins())

	// Scenario 2: Empty response
	restMock.Registry().GetAllAdmins().WithSuccess(helpers.CreateEmptyAdminList())

	// Scenario 3: Error cases
	restMock.Registry().GetAllAdmins().WithUnauthorized()
	restMock.Registry().GetAllAdmins().WithInternalError()
	restMock.Registry().GetAllAdmins().WithTimeout()

	// Scenario 4: Custom error
	restMock.Registry().GetAllAdmins().WithError(418, "I'm a teapot")

	restMock.Setup()
}

// ExampleTestIntegration shows how to integrate with actual tests
func ExampleTestIntegration() {
	// This would typically be in a test function
	restMock := mocks.NewRestClientMock()

	// Setup default successful responses
	restMock.SetupDefaultMocks()

	// Override specific endpoints for this test
	restMock.Registry().GetAllAdmins().WithSuccess(helpers.CreateSingleAdmin(1, "test_admin"))

	// Setup the mock
	restMock.Setup()

	// Test code would go here...
	// result := someBusinessLogic()
	// assert.True(t, result)

	// Verify calls were made
	// assert.True(t, restMock.VerifyFetchUserRolesCalled())

	// Reset for next test
	restMock.Reset()
}
