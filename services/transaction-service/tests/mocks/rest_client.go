package mocks

import (
	httpClient "transaction-service/core/http"
	"transaction-service/internal/integration/rest"
	"transaction-service/tests/mocks/helpers"
	"transaction-service/tests/mocks/registry"
	"transaction-service/tests/mocks/services"
)

// MockHTTPClientAdapter adapts MockHTTPClient to services.MockHTTPClientInterface
type MockHTTPClientAdapter struct {
	client *MockHTTPClient
}

// NewMockHTTPClientAdapter creates a new adapter
func NewMockHTTPClientAdapter(client *MockHTTPClient) *MockHTTPClientAdapter {
	return &MockHTTPClientAdapter{client: client}
}

// SetJSONResponse delegates to the underlying client
func (a *MockHTTPClientAdapter) SetJSONResponse(method, url, jsonBody string) {
	a.client.SetJSONResponse(method, url, jsonBody)
}

// SetResponse delegates to the underlying client
func (a *MockHTTPClientAdapter) SetResponse(method, url string, statusCode int, body string) {
	a.client.SetResponse(method, url, statusCode, body)
}

// SetResponseWithHeaders delegates to the underlying client
func (a *MockHTTPClientAdapter) SetResponseWithHeaders(method, url string, statusCode int, body string, headers map[string]string) {
	a.client.SetResponseWithHeaders(method, url, statusCode, body, headers)
}

// SetErrorResponse delegates to the underlying client
func (a *MockHTTPClientAdapter) SetErrorResponse(method, url string, statusCode int, errorMessage string) {
	a.client.SetErrorResponse(method, url, statusCode, errorMessage)
}

// SetError delegates to the underlying client
func (a *MockHTTPClientAdapter) SetError(method, url string, err error) {
	a.client.SetError(method, url, err)
}

// GetRequestsForURL delegates to the underlying client and converts types
func (a *MockHTTPClientAdapter) GetRequestsForURL(urlPattern string) []services.MockRequest {
	requests := a.client.GetRequestsForURL(urlPattern)
	result := make([]services.MockRequest, len(requests))
	for i, req := range requests {
		result[i] = services.MockRequest{
			Method:  req.Method,
			URL:     req.URL,
			Headers: req.Headers,
			Body:    req.Body,
		}
	}
	return result
}

// RestClientMock provides convenient methods for mocking REST service calls
type RestClientMock struct {
	mockClient   *MockHTTPClient
	authService  *services.AuthServiceMock
	mockRegistry *registry.MockRegistry
	authRegistry *services.AuthServiceRegistry
}

// NewRestClientMock creates a new REST client mock
func NewRestClientMock() *RestClientMock {
	mockClient := NewMockHTTPClient()
	adapter := NewMockHTTPClientAdapter(mockClient)
	mockRegistry := registry.NewMockRegistry(adapter)
	authRegistry := services.NewAuthServiceRegistry(adapter)

	return &RestClientMock{
		mockClient:   mockClient,
		authService:  services.NewAuthServiceMock(adapter),
		mockRegistry: mockRegistry,
		authRegistry: authRegistry,
	}
}

// Setup configures the mock client in the centralized HTTP client
func (r *RestClientMock) Setup() {
	httpClient.SetMockClient(r.mockClient)
}

// Reset clears all mock responses and captured requests
func (r *RestClientMock) Reset() {
	r.mockClient.Reset()
}

// GetMockClient returns the underlying mock HTTP client
func (r *RestClientMock) GetMockClient() *MockHTTPClient {
	return r.mockClient
}

// GetAuthService returns the auth service mock
func (r *RestClientMock) GetAuthService() *services.AuthServiceMock {
	return r.authService
}

// GetMockRegistry returns the mock registry for advanced configuration
func (r *RestClientMock) GetMockRegistry() *registry.MockRegistry {
	return r.mockRegistry
}

// GetAuthRegistry returns the auth service registry for fluent API
func (r *RestClientMock) GetAuthRegistry() *services.AuthServiceRegistry {
	return r.authRegistry
}

// Registry returns the auth service group for fluent mock configuration
func (r *RestClientMock) Registry() *services.AuthServiceGroup {
	return r.authRegistry.Register(r.mockRegistry, "auth").(*services.AuthServiceGroup)
}

// Auth Service Delegation Methods (for backward compatibility)

// MockFetchUserRolesSuccess delegates to auth service mock
func (r *RestClientMock) MockFetchUserRolesSuccess(admins []rest.AdminsDto) {
	r.authService.MockFetchUserRolesSuccess(admins)
}

// MockFetchUserRolesError delegates to auth service mock
func (r *RestClientMock) MockFetchUserRolesError(statusCode int, errorMessage string) {
	r.authService.MockFetchUserRolesError(statusCode, errorMessage)
}

// MockFetchUserRolesNetworkError delegates to auth service mock
func (r *RestClientMock) MockFetchUserRolesNetworkError(err error) {
	r.authService.MockFetchUserRolesNetworkError(err)
}

// MockFetchUserRolesUnauthorized delegates to auth service mock
func (r *RestClientMock) MockFetchUserRolesUnauthorized() {
	r.authService.MockFetchUserRolesUnauthorized()
}

// MockFetchUserRolesInternalError delegates to auth service mock
func (r *RestClientMock) MockFetchUserRolesInternalError() {
	r.authService.MockFetchUserRolesInternalError()
}

// MockFetchUserRolesTimeout delegates to auth service mock
func (r *RestClientMock) MockFetchUserRolesTimeout() {
	r.authService.MockFetchUserRolesTimeout()
}

// VerifyFetchUserRolesCalled delegates to auth service mock
func (r *RestClientMock) VerifyFetchUserRolesCalled() bool {
	return r.authService.VerifyFetchUserRolesCalled()
}

// VerifyFetchUserRolesCalledWithAuth delegates to auth service mock
func (r *RestClientMock) VerifyFetchUserRolesCalledWithAuth(expectedToken string) bool {
	return r.authService.VerifyFetchUserRolesCalledWithAuth(expectedToken)
}

// GetFetchUserRolesCallCount delegates to auth service mock
func (r *RestClientMock) GetFetchUserRolesCallCount() int {
	return r.authService.GetFetchUserRolesCallCount()
}

// SetupDefaultMocks sets up common successful responses
func (r *RestClientMock) SetupDefaultMocks() {
	r.MockFetchUserRolesSuccess(helpers.CreateTestAdmins())
}

// Helper functions for backward compatibility

// CreateTestAdmins creates test admin data for common scenarios
func CreateTestAdmins() []rest.AdminsDto {
	return helpers.CreateTestAdmins()
}

// CreateSingleAdmin creates a single admin for testing
func CreateSingleAdmin(userID uint, role string) []rest.AdminsDto {
	return helpers.CreateSingleAdmin(userID, role)
}
