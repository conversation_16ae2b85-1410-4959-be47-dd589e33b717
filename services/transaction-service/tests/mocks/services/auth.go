package services

import (
	"fmt"
	"net/http"
	"transaction-service/core/config"
	"transaction-service/internal/integration/rest"
)

// MockRequest represents a captured HTTP request
type MockRequest struct {
	Method  string
	URL     string
	Headers map[string]string
	Body    string
}

// MockHTTPClientInterface defines the interface for mock HTTP client
type MockHTTPClientInterface interface {
	SetJSONResponse(method, url, jsonBody string)
	SetResponse(method, url string, statusCode int, body string)
	SetResponseWithHeaders(method, url string, statusCode int, body string, headers map[string]string)
	SetErrorResponse(method, url string, statusCode int, errorMessage string)
	SetError(method, url string, err error)
	GetRequestsForURL(urlPattern string) []MockRequest
}

// AuthServiceMock handles mock responses for auth service endpoints
type AuthServiceMock struct {
	mockClient MockHTTPClientInterface
}

// AuthServiceRegistry provides registry-style mock configuration for auth service
type AuthServiceRegistry struct {
	client MockHTTPClientInterface
}

// NewAuthServiceMock creates a new auth service mock
func NewAuthServiceMock(mockClient MockHTTPClientInterface) *AuthServiceMock {
	return &AuthServiceMock{
		mockClient: mockClient,
	}
}

// MockFetchUserRolesSuccess mocks a successful FetchUserRoles response
func (a *AuthServiceMock) MockFetchUserRolesSuccess(admins []rest.AdminsDto) {
	url := a.buildAuthServiceURL("api/v1/user/get-all-admins")
	jsonBody := a.adminsToJSON(admins)
	a.mockClient.SetJSONResponse("GET", url, jsonBody)
}

// MockFetchUserRolesError mocks an error response for FetchUserRoles
func (a *AuthServiceMock) MockFetchUserRolesError(statusCode int, errorMessage string) {
	url := a.buildAuthServiceURL("api/v1/user/get-all-admins")
	a.mockClient.SetErrorResponse("GET", url, statusCode, errorMessage)
}

// MockFetchUserRolesNetworkError mocks a network error for FetchUserRoles
func (a *AuthServiceMock) MockFetchUserRolesNetworkError(err error) {
	url := a.buildAuthServiceURL("api/v1/user/get-all-admins")
	a.mockClient.SetError("GET", url, err)
}

// MockFetchUserRolesUnauthorized mocks an unauthorized response
func (a *AuthServiceMock) MockFetchUserRolesUnauthorized() {
	a.MockFetchUserRolesError(http.StatusUnauthorized, "Unauthorized")
}

// MockFetchUserRolesInternalError mocks an internal server error
func (a *AuthServiceMock) MockFetchUserRolesInternalError() {
	a.MockFetchUserRolesError(http.StatusInternalServerError, "Internal Server Error")
}

// MockFetchUserRolesTimeout mocks a timeout error
func (a *AuthServiceMock) MockFetchUserRolesTimeout() {
	a.MockFetchUserRolesNetworkError(fmt.Errorf("request timeout"))
}

// Verification methods

// VerifyFetchUserRolesCalled verifies that FetchUserRoles was called
func (a *AuthServiceMock) VerifyFetchUserRolesCalled() bool {
	url := a.buildAuthServiceURL("api/v1/user/get-all-admins")
	requests := a.mockClient.GetRequestsForURL("api/v1/user/get-all-admins")

	for _, req := range requests {
		if req.Method == "GET" && req.URL == url {
			return true
		}
	}
	return false
}

// VerifyFetchUserRolesCalledWithAuth verifies that FetchUserRoles was called with proper authorization
func (a *AuthServiceMock) VerifyFetchUserRolesCalledWithAuth(expectedToken string) bool {
	requests := a.mockClient.GetRequestsForURL("api/v1/user/get-all-admins")

	for _, req := range requests {
		if req.Method == "GET" {
			if authHeader, exists := req.Headers["Authorization"]; exists {
				expectedAuth := "Bearer " + expectedToken
				if authHeader == expectedAuth {
					return true
				}
			}
		}
	}
	return false
}

// GetFetchUserRolesCallCount returns the number of times FetchUserRoles was called
func (a *AuthServiceMock) GetFetchUserRolesCallCount() int {
	requests := a.mockClient.GetRequestsForURL("api/v1/user/get-all-admins")
	count := 0
	for _, req := range requests {
		if req.Method == "GET" {
			count++
		}
	}
	return count
}

// Helper methods

// buildAuthServiceURL builds the full URL for auth service endpoints
func (a *AuthServiceMock) buildAuthServiceURL(endpoint string) string {
	return config.Get().AuthService.Host + endpoint
}

// adminsToJSON converts AdminsDto slice to JSON string
func (a *AuthServiceMock) adminsToJSON(admins []rest.AdminsDto) string {
	if len(admins) == 0 {
		return "[]"
	}

	jsonStr := "["
	for i, admin := range admins {
		if i > 0 {
			jsonStr += ","
		}
		jsonStr += fmt.Sprintf(`{"UserId":%d,"Role":"%s"}`, admin.UserID, admin.Role)
	}
	jsonStr += "]"

	return jsonStr
}

// Registry-style Auth Service Mock

// NewAuthServiceRegistry creates a new auth service registry
func NewAuthServiceRegistry(client MockHTTPClientInterface) *AuthServiceRegistry {
	return &AuthServiceRegistry{client: client}
}

// Register implements the MockServiceInterface for registry-style configuration
func (a *AuthServiceRegistry) Register(registry interface{}, serviceName string) interface{} {
	// This would be implemented to work with the MockRegistry
	// For now, return a mock service group-like interface
	return &AuthServiceGroup{
		serviceName: serviceName,
		client:      a.client,
	}
}

// AuthServiceGroup provides fluent API for auth service mocking
type AuthServiceGroup struct {
	serviceName string
	client      MockHTTPClientInterface
}

// GetAllAdmins configures mock for the get all admins endpoint
func (g *AuthServiceGroup) GetAllAdmins() *AuthEndpointBuilder {
	return &AuthEndpointBuilder{
		method:   "GET",
		endpoint: "api/v1/user/get-all-admins",
		client:   g.client,
	}
}

// Login configures mock for the login endpoint
func (g *AuthServiceGroup) Login() *AuthEndpointBuilder {
	return &AuthEndpointBuilder{
		method:   "POST",
		endpoint: "api/v1/auth/login",
		client:   g.client,
	}
}

// RefreshToken configures mock for the refresh token endpoint
func (g *AuthServiceGroup) RefreshToken() *AuthEndpointBuilder {
	return &AuthEndpointBuilder{
		method:   "POST",
		endpoint: "api/v1/auth/refresh",
		client:   g.client,
	}
}

// AuthEndpointBuilder provides fluent API for building auth endpoint mocks
type AuthEndpointBuilder struct {
	method   string
	endpoint string
	client   MockHTTPClientInterface
}

// WithSuccess configures a successful response
func (b *AuthEndpointBuilder) WithSuccess(data interface{}) *AuthEndpointBuilder {
	url := config.Get().AuthService.Host + b.endpoint

	var jsonBody string
	if admins, ok := data.([]rest.AdminsDto); ok {
		jsonBody = b.adminsToJSON(admins)
	} else if str, ok := data.(string); ok {
		jsonBody = str
	} else {
		jsonBody = `{"success":true}`
	}

	b.client.SetJSONResponse(b.method, url, jsonBody)
	return b
}

// WithError configures an error response
func (b *AuthEndpointBuilder) WithError(statusCode int, message string) *AuthEndpointBuilder {
	url := config.Get().AuthService.Host + b.endpoint
	b.client.SetErrorResponse(b.method, url, statusCode, message)
	return b
}

// WithUnauthorized configures an unauthorized response
func (b *AuthEndpointBuilder) WithUnauthorized() *AuthEndpointBuilder {
	return b.WithError(http.StatusUnauthorized, "Unauthorized")
}

// WithInternalError configures an internal server error response
func (b *AuthEndpointBuilder) WithInternalError() *AuthEndpointBuilder {
	return b.WithError(http.StatusInternalServerError, "Internal Server Error")
}

// WithTimeout configures a timeout error
func (b *AuthEndpointBuilder) WithTimeout() *AuthEndpointBuilder {
	url := config.Get().AuthService.Host + b.endpoint
	b.client.SetError(b.method, url, fmt.Errorf("request timeout"))
	return b
}

// Helper method for JSON conversion
func (b *AuthEndpointBuilder) adminsToJSON(admins []rest.AdminsDto) string {
	if len(admins) == 0 {
		return "[]"
	}

	jsonStr := "["
	for i, admin := range admins {
		if i > 0 {
			jsonStr += ","
		}
		jsonStr += fmt.Sprintf(`{"UserId":%d,"Role":"%s"}`, admin.UserID, admin.Role)
	}
	jsonStr += "]"

	return jsonStr
}
