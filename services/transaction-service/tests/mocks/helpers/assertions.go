package helpers

import (
	"strings"
	"testing"
)

// Test Assertion Helpers

// AssertRequestMade checks if a request was made to a specific URL pattern
func AssertRequestMade(t *testing.T, requests []interface{}, urlPattern string) bool {
	t.Helper()
	for _, req := range requests {
		if reqMap, ok := req.(map[string]interface{}); ok {
			if url, exists := reqMap["URL"]; exists {
				if urlStr, ok := url.(string); ok && strings.Contains(urlStr, urlPattern) {
					return true
				}
			}
		}
	}
	return false
}

// AssertRequestCount checks if the expected number of requests were made
func AssertRequestCount(t *testing.T, requests []interface{}, expected int) bool {
	t.Helper()
	return len(requests) == expected
}

// AssertRequestMethod checks if a request was made with the expected HTTP method
func AssertRequestMethod(t *testing.T, requests []interface{}, method string) bool {
	t.Helper()
	for _, req := range requests {
		if reqMap, ok := req.(map[string]interface{}); ok {
			if reqMethod, exists := reqMap["Method"]; exists {
				if methodStr, ok := reqMethod.(string); ok && methodStr == method {
					return true
				}
			}
		}
	}
	return false
}

// AssertRequestHeader checks if a request was made with the expected header
func AssertRequestHeader(t *testing.T, requests []interface{}, headerName, expectedValue string) bool {
	t.Helper()
	for _, req := range requests {
		if reqMap, ok := req.(map[string]interface{}); ok {
			if headers, exists := reqMap["Headers"]; exists {
				if headersMap, ok := headers.(map[string]string); ok {
					if value, exists := headersMap[headerName]; exists && value == expectedValue {
						return true
					}
				}
			}
		}
	}
	return false
}

// AssertAuthorizationHeader checks if a request was made with proper authorization
func AssertAuthorizationHeader(t *testing.T, requests []interface{}, expectedToken string) bool {
	t.Helper()
	expectedAuth := "Bearer " + expectedToken
	return AssertRequestHeader(t, requests, "Authorization", expectedAuth)
}

// Mock Response Validation Helpers

// ValidateJSONResponse checks if a response is valid JSON
func ValidateJSONResponse(response string) bool {
	// Simple validation - check if it starts with { or [
	trimmed := strings.TrimSpace(response)
	return strings.HasPrefix(trimmed, "{") || strings.HasPrefix(trimmed, "[")
}

// ValidateErrorResponse checks if a response contains error information
func ValidateErrorResponse(response string) bool {
	return strings.Contains(response, "error") || strings.Contains(response, "Error")
}

// Test Data Validation Helpers

// ValidateAdminData checks if admin data has required fields
func ValidateAdminData(data map[string]interface{}) bool {
	_, hasUserID := data["UserID"]
	_, hasRole := data["Role"]
	return hasUserID && hasRole
}

// ValidateAdminRole checks if a role is valid
func ValidateAdminRole(role string) bool {
	validRoles := []string{"super_admin", "admin", "user", "viewer", "guest"}
	for _, validRole := range validRoles {
		if role == validRole {
			return true
		}
	}
	return false
}
