package helpers

import (
	"fmt"
	"transaction-service/internal/integration/rest"
)

// Admin Data Helpers

// CreateTestAdmins creates test admin data for common scenarios
func CreateTestAdmins() []rest.AdminsDto {
	return []rest.AdminsDto{
		{UserID: 1, Role: "super_admin"},
		{UserID: 2, Role: "admin"},
		{UserID: 3, Role: "user"},
	}
}

// CreateSingleAdmin creates a single admin for testing
func CreateSingleAdmin(userID uint, role string) []rest.AdminsDto {
	return []rest.AdminsDto{
		{UserID: userID, Role: role},
	}
}

// CreateAdminWithRole creates an admin with specific role for testing
func CreateAdminWithRole(role string) []rest.AdminsDto {
	return CreateSingleAdmin(1, role)
}

// CreateMultipleAdmins creates multiple admins with specified roles
func CreateMultipleAdmins(roles []string) []rest.AdminsDto {
	admins := make([]rest.AdminsDto, len(roles))
	for i, role := range roles {
		admins[i] = rest.AdminsDto{
			UserID: uint(i + 1),
			Role:   role,
		}
	}
	return admins
}

// CreateEmptyAdminList creates an empty admin list for testing
func CreateEmptyAdminList() []rest.AdminsDto {
	return []rest.AdminsDto{}
}

// CreateAdminsWithUserIDs creates admins with specific user IDs
func CreateAdminsWithUserIDs(userIDs []uint, role string) []rest.AdminsDto {
	admins := make([]rest.AdminsDto, len(userIDs))
	for i, userID := range userIDs {
		admins[i] = rest.AdminsDto{
			UserID: userID,
			Role:   role,
		}
	}
	return admins
}

// JSON Conversion Helpers

// AdminsToJSON converts AdminsDto slice to JSON string
func AdminsToJSON(admins []rest.AdminsDto) string {
	if len(admins) == 0 {
		return "[]"
	}

	jsonStr := "["
	for i, admin := range admins {
		if i > 0 {
			jsonStr += ","
		}
		jsonStr += fmt.Sprintf(`{"UserId":%d,"Role":"%s"}`, admin.UserID, admin.Role)
	}
	jsonStr += "]"

	return jsonStr
}

// URL Building Helpers

// BuildServiceURL builds a URL for a service endpoint
func BuildServiceURL(baseURL, endpoint string) string {
	return baseURL + endpoint
}

// Common Test Scenarios

// GetCommonTestScenarios returns common test scenarios for admins
func GetCommonTestScenarios() map[string][]rest.AdminsDto {
	return map[string][]rest.AdminsDto{
		"empty":        CreateEmptyAdminList(),
		"single_admin": CreateSingleAdmin(1, "admin"),
		"super_admin":  CreateSingleAdmin(1, "super_admin"),
		"multiple":     CreateTestAdmins(),
		"users_only":   CreateMultipleAdmins([]string{"user", "user", "user"}),
		"mixed_roles":  CreateMultipleAdmins([]string{"super_admin", "admin", "user", "viewer"}),
	}
}
