package helpers

import (
	"errors"
	"net/http"
)

// Common Error Scenarios

// GetNetworkErrors returns common network error scenarios
func GetNetworkErrors() map[string]error {
	return map[string]error{
		"timeout":           errors.New("request timeout"),
		"connection_refused": errors.New("connection refused"),
		"dns_error":         errors.New("no such host"),
		"network_unreachable": errors.New("network is unreachable"),
	}
}

// GetHTTPErrorCodes returns common HTTP error status codes
func GetHTTPErrorCodes() map[string]int {
	return map[string]int{
		"bad_request":          http.StatusBadRequest,
		"unauthorized":         http.StatusUnauthorized,
		"forbidden":           http.StatusForbidden,
		"not_found":           http.StatusNotFound,
		"method_not_allowed":  http.StatusMethodNotAllowed,
		"internal_server_error": http.StatusInternalServerError,
		"bad_gateway":         http.StatusBadGateway,
		"service_unavailable": http.StatusServiceUnavailable,
		"gateway_timeout":     http.StatusGatewayTimeout,
	}
}

// GetSuccessStatusCodes returns common success status codes
func GetSuccessStatusCodes() map[string]int {
	return map[string]int{
		"ok":      http.StatusOK,
		"created": http.StatusCreated,
		"accepted": http.StatusAccepted,
		"no_content": http.StatusNoContent,
	}
}

// Response Templates

// GetErrorResponseTemplates returns common error response templates
func GetErrorResponseTemplates() map[string]string {
	return map[string]string{
		"unauthorized": `{"error":"Unauthorized","message":"Invalid or missing authentication token"}`,
		"forbidden":    `{"error":"Forbidden","message":"Insufficient permissions"}`,
		"not_found":    `{"error":"Not Found","message":"Resource not found"}`,
		"internal_error": `{"error":"Internal Server Error","message":"An unexpected error occurred"}`,
		"bad_request":  `{"error":"Bad Request","message":"Invalid request parameters"}`,
		"validation_error": `{"error":"Validation Error","message":"Request validation failed","details":[]}`,
	}
}

// GetSuccessResponseTemplates returns common success response templates
func GetSuccessResponseTemplates() map[string]string {
	return map[string]string{
		"empty_list": `[]`,
		"success_message": `{"message":"Operation completed successfully"}`,
		"created_resource": `{"id":1,"message":"Resource created successfully"}`,
	}
}

// Test Configuration Helpers

// GetTestEndpoints returns common test endpoints
func GetTestEndpoints() map[string]string {
	return map[string]string{
		"auth_get_admins":    "api/v1/user/get-all-admins",
		"auth_login":         "api/v1/auth/login",
		"auth_refresh":       "api/v1/auth/refresh",
		"user_profile":       "api/v1/user/profile",
		"user_permissions":   "api/v1/user/permissions",
	}
}

// GetTestHeaders returns common test headers
func GetTestHeaders() map[string]map[string]string {
	return map[string]map[string]string{
		"json": {
			"Content-Type": "application/json",
		},
		"auth": {
			"Authorization": "Bearer test-token",
			"Content-Type":  "application/json",
		},
		"form": {
			"Content-Type": "application/x-www-form-urlencoded",
		},
	}
}
