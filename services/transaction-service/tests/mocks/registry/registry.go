package registry

import (
	"github.com/elliotchance/orderedmap"
	"transaction-service/tests/mocks/services"
)

// MockServiceInterface defines the interface for mock services
type MockServiceInterface interface {
	Register(registry *MockRegistry, serviceName string) *MockServiceGroup
}

// MockRegistry manages mock service registration similar to gin router
type MockRegistry struct {
	services *orderedmap.OrderedMap
	client   services.MockHTTPClientInterface
}

// MockServiceGroup represents a group of mock endpoints for a service
type MockServiceGroup struct {
	serviceName string
	registry    *MockRegistry
	endpoints   map[string]*MockEndpoint
}

// MockEndpoint represents a single mock endpoint configuration
type MockEndpoint struct {
	Method      string
	URL         string
	StatusCode  int
	Response    string
	Headers     map[string]string
	Error       error
	Middleware  []MockMiddleware
}

// MockMiddleware defines middleware for mock responses
type MockMiddleware func(*MockEndpoint) *MockEndpoint

// NewMockRegistry creates a new mock registry
func NewMockRegistry(client services.MockHTTPClientInterface) *MockRegistry {
	return &MockRegistry{
		services: orderedmap.NewOrderedMap(),
		client:   client,
	}
}

// Register registers a mock service with the registry
func (r *MockRegistry) Register(serviceName string, service MockServiceInterface) {
	r.services.Set(serviceName, service)
}

// Setup configures all registered mock services
func (r *MockRegistry) Setup() {
	for s := r.services.Front(); s != nil; s = s.Next() {
		if service, ok := s.Value.(MockServiceInterface); ok {
			service.Register(r, s.Key.(string))
		}
	}
}

// Group creates a new mock service group
func (r *MockRegistry) Group(serviceName string) *MockServiceGroup {
	return &MockServiceGroup{
		serviceName: serviceName,
		registry:    r,
		endpoints:   make(map[string]*MockEndpoint),
	}
}

// Reset clears all mock responses
func (r *MockRegistry) Reset() {
	// Delegate to the underlying mock client
	if resetter, ok := r.client.(interface{ Reset() }); ok {
		resetter.Reset()
	}
}

// MockServiceGroup methods

// GET registers a GET endpoint mock
func (g *MockServiceGroup) GET(endpoint string) *MockEndpoint {
	return g.addEndpoint("GET", endpoint)
}

// POST registers a POST endpoint mock
func (g *MockServiceGroup) POST(endpoint string) *MockEndpoint {
	return g.addEndpoint("POST", endpoint)
}

// PUT registers a PUT endpoint mock
func (g *MockServiceGroup) PUT(endpoint string) *MockEndpoint {
	return g.addEndpoint("PUT", endpoint)
}

// DELETE registers a DELETE endpoint mock
func (g *MockServiceGroup) DELETE(endpoint string) *MockEndpoint {
	return g.addEndpoint("DELETE", endpoint)
}

// PATCH registers a PATCH endpoint mock
func (g *MockServiceGroup) PATCH(endpoint string) *MockEndpoint {
	return g.addEndpoint("PATCH", endpoint)
}

// addEndpoint creates and registers a new mock endpoint
func (g *MockServiceGroup) addEndpoint(method, endpoint string) *MockEndpoint {
	mockEndpoint := &MockEndpoint{
		Method:     method,
		URL:        endpoint,
		StatusCode: 200,
		Headers:    make(map[string]string),
		Middleware: make([]MockMiddleware, 0),
	}
	
	key := method + ":" + endpoint
	g.endpoints[key] = mockEndpoint
	
	return mockEndpoint
}

// MockEndpoint methods for fluent configuration

// WithStatus sets the response status code
func (e *MockEndpoint) WithStatus(statusCode int) *MockEndpoint {
	e.StatusCode = statusCode
	return e
}

// WithResponse sets the response body
func (e *MockEndpoint) WithResponse(response string) *MockEndpoint {
	e.Response = response
	return e
}

// WithJSON sets a JSON response with appropriate headers
func (e *MockEndpoint) WithJSON(jsonResponse string) *MockEndpoint {
	e.Response = jsonResponse
	e.Headers["Content-Type"] = "application/json"
	return e
}

// WithHeaders sets response headers
func (e *MockEndpoint) WithHeaders(headers map[string]string) *MockEndpoint {
	for key, value := range headers {
		e.Headers[key] = value
	}
	return e
}

// WithError sets an error response
func (e *MockEndpoint) WithError(err error) *MockEndpoint {
	e.Error = err
	return e
}

// Use applies middleware to the endpoint
func (e *MockEndpoint) Use(middleware ...MockMiddleware) *MockEndpoint {
	e.Middleware = append(e.Middleware, middleware...)
	return e
}

// Apply configures the mock endpoint in the underlying client
func (e *MockEndpoint) Apply(client services.MockHTTPClientInterface) {
	// Apply middleware
	finalEndpoint := e
	for _, middleware := range e.Middleware {
		finalEndpoint = middleware(finalEndpoint)
	}
	
	// Configure the mock response
	if finalEndpoint.Error != nil {
		client.SetError(finalEndpoint.Method, finalEndpoint.URL, finalEndpoint.Error)
	} else if len(finalEndpoint.Headers) > 0 {
		client.SetResponseWithHeaders(finalEndpoint.Method, finalEndpoint.URL, 
			finalEndpoint.StatusCode, finalEndpoint.Response, finalEndpoint.Headers)
	} else {
		client.SetResponse(finalEndpoint.Method, finalEndpoint.URL, 
			finalEndpoint.StatusCode, finalEndpoint.Response)
	}
}
