# Mock Registry System

A gin-router-style registration system for organizing and configuring mock responses in tests.

## Overview

The Mock Registry System provides a fluent API similar to Gin router for organizing mock HTTP responses. It supports:

- **Service Groups**: Organize mocks by service (auth, user, transaction, etc.)
- **HTTP Methods**: Support for GET, POST, PUT, DELETE, PATCH
- **Middleware**: Reusable middleware for common response patterns
- **Fluent API**: Chain methods for easy configuration
- **Type Safety**: Strongly typed interfaces and builders

## Quick Start

```go
// Create a REST client mock
restMock := mocks.NewRestClientMock()
mockRegistry := restMock.GetMockRegistry()
adapter := mocks.NewMockHTTPClientAdapter(restMock.GetMockClient())

// Create service groups
authService := mockRegistry.Group("auth")
userService := mockRegistry.Group("user")

// Configure endpoints with fluent API
authService.GET("api/v1/user/profile").
    WithJSON(`{"id":1,"name":"<PERSON>"}`).
    Use(registry.WithAuth("bearer-token"), registry.WithCORS()).
    Apply(adapter)

userService.POST("api/v1/user/update").
    WithStatus(201).
    WithJSON(`{"message":"User updated"}`).
    Use(registry.WithContentType("application/json")).
    Apply(adapter)

// Setup the mock
restMock.Setup()
```

## Service Groups

Service groups organize related endpoints together:

```go
authService := mockRegistry.Group("auth")
userService := mockRegistry.Group("user")
transactionService := mockRegistry.Group("transaction")
```

## HTTP Methods

All standard HTTP methods are supported:

```go
group.GET("api/v1/resource")
group.POST("api/v1/resource")
group.PUT("api/v1/resource/1")
group.DELETE("api/v1/resource/1")
group.PATCH("api/v1/resource/1")
```

## Response Configuration

### Basic Responses

```go
endpoint.WithStatus(200)
endpoint.WithResponse("plain text response")
endpoint.WithJSON(`{"key":"value"}`)
```

### Headers

```go
endpoint.WithHeaders(map[string]string{
    "Content-Type": "application/json",
    "X-Custom-Header": "value",
})
```

### Errors

```go
endpoint.WithError(errors.New("network error"))
```

## Middleware

Middleware provides reusable response patterns:

### Authentication

```go
endpoint.Use(registry.WithAuth("required-token"))
```

### CORS

```go
endpoint.Use(registry.WithCORS())
```

### Content Type

```go
endpoint.Use(registry.WithContentType("application/json"))
endpoint.Use(registry.WithJSON(`{"data":"value"}`)) // Sets content type automatically
```

### Error Responses

```go
endpoint.Use(registry.WithUnauthorized())
endpoint.Use(registry.WithForbidden())
endpoint.Use(registry.WithNotFound())
endpoint.Use(registry.WithInternalError())
endpoint.Use(registry.WithBadRequest("Custom message"))
```

### Success Responses

```go
endpoint.Use(registry.WithCreated(123)) // Resource ID
endpoint.Use(registry.WithNoContent())
```

### Rate Limiting

```go
endpoint.Use(registry.WithRateLimit(100)) // Requests per minute
```

### Custom Headers

```go
endpoint.Use(registry.WithCustomHeaders(map[string]string{
    "X-API-Version": "v1",
    "X-Request-ID": "12345",
}))
```

### Conditional Responses

```go
endpoint.Use(registry.WithConditionalResponse(
    func() bool { return isTestMode },
    registry.WithJSON(`{"test":true}`),
    registry.WithUnauthorized(),
))
```

## Complete Example

```go
func setupMocks() {
    restMock := mocks.NewRestClientMock()
    mockRegistry := restMock.GetMockRegistry()
    adapter := mocks.NewMockHTTPClientAdapter(restMock.GetMockClient())
    
    // Auth service
    authService := mockRegistry.Group("auth")
    authService.POST("api/v1/auth/login").
        WithJSON(`{"token":"jwt-token","expires_in":3600}`).
        Use(registry.WithContentType("application/json")).
        Apply(adapter)
    
    authService.GET("api/v1/user/profile").
        WithJSON(`{"id":1,"name":"John Doe","role":"admin"}`).
        Use(registry.WithAuth("bearer-token"), registry.WithCORS()).
        Apply(adapter)
    
    // Error cases
    authService.GET("api/v1/admin/restricted").
        Use(registry.WithForbidden()).
        Apply(adapter)
    
    // User service
    userService := mockRegistry.Group("user")
    userService.PUT("api/v1/user/1").
        Use(registry.WithCreated(1)).
        Apply(adapter)
    
    userService.DELETE("api/v1/user/999").
        Use(registry.WithNotFound()).
        Apply(adapter)
    
    restMock.Setup()
}
```

## Integration with Existing Code

The registry system is fully compatible with existing mock code:

```go
// Traditional approach (still works)
restMock.MockFetchUserRolesSuccess(helpers.CreateTestAdmins())

// New registry approach
restMock.Registry().GetAllAdmins().WithSuccess(helpers.CreateTestAdmins())

// Advanced registry approach
mockRegistry.Group("auth").GET("api/v1/user/get-all-admins").
    WithJSON(helpers.AdminsToJSON(helpers.CreateTestAdmins())).
    Apply(adapter)
```

## Best Practices

1. **Group by Service**: Organize endpoints by service for better maintainability
2. **Use Middleware**: Leverage middleware for common patterns like auth, CORS, etc.
3. **Consistent Naming**: Use consistent endpoint naming conventions
4. **Error Scenarios**: Always test both success and error scenarios
5. **Reset Between Tests**: Call `restMock.Reset()` between tests for isolation
