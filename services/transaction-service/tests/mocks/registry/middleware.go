package registry

import (
	"fmt"
	"net/http"
	"time"
)

// Common Mock Middleware Functions

// WithDelay adds a delay to the mock response (simulates network latency)
func WithDelay(duration time.Duration) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		// Note: In a real implementation, you might want to add delay logic
		// For now, we'll just return the endpoint as-is since HTTP mocks
		// don't typically support delays in the same way
		return endpoint
	}
}

// WithAuth adds authentication requirements to the mock
func WithAuth(requiredToken string) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		// This middleware would typically validate auth headers
		// For mock purposes, we can add this as metadata
		if endpoint.Headers == nil {
			endpoint.Headers = make(map[string]string)
		}
		endpoint.Headers["X-Required-Auth"] = requiredToken
		return endpoint
	}
}

// WithCORS adds CORS headers to the mock response
func WithCORS() MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		if endpoint.Headers == nil {
			endpoint.Headers = make(map[string]string)
		}
		endpoint.Headers["Access-Control-Allow-Origin"] = "*"
		endpoint.Headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
		endpoint.Headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
		return endpoint
	}
}

// WithContentType sets the content type header
func WithContentType(contentType string) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		if endpoint.Headers == nil {
			endpoint.Headers = make(map[string]string)
		}
		endpoint.Headers["Content-Type"] = contentType
		return endpoint
	}
}

// WithJSON sets a JSON response with appropriate content type
func WithJSON(jsonResponse string) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.Response = jsonResponse
		if endpoint.Headers == nil {
			endpoint.Headers = make(map[string]string)
		}
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// WithRateLimit simulates rate limiting responses
func WithRateLimit(requestsPerMinute int) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		// Add rate limit headers
		if endpoint.Headers == nil {
			endpoint.Headers = make(map[string]string)
		}
		endpoint.Headers["X-RateLimit-Limit"] = fmt.Sprintf("%d", requestsPerMinute)
		endpoint.Headers["X-RateLimit-Remaining"] = fmt.Sprintf("%d", requestsPerMinute-1)
		return endpoint
	}
}

// WithCustomHeaders adds custom headers to the response
func WithCustomHeaders(headers map[string]string) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		if endpoint.Headers == nil {
			endpoint.Headers = make(map[string]string)
		}
		for key, value := range headers {
			endpoint.Headers[key] = value
		}
		return endpoint
	}
}

// Error Response Middleware

// WithUnauthorized sets up an unauthorized response
func WithUnauthorized() MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusUnauthorized
		endpoint.Response = `{"error":"Unauthorized","message":"Authentication required"}`
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// WithForbidden sets up a forbidden response
func WithForbidden() MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusForbidden
		endpoint.Response = `{"error":"Forbidden","message":"Insufficient permissions"}`
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// WithNotFound sets up a not found response
func WithNotFound() MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusNotFound
		endpoint.Response = `{"error":"Not Found","message":"Resource not found"}`
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// WithInternalError sets up an internal server error response
func WithInternalError() MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusInternalServerError
		endpoint.Response = `{"error":"Internal Server Error","message":"An unexpected error occurred"}`
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// WithBadRequest sets up a bad request response
func WithBadRequest(message string) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusBadRequest
		if message == "" {
			message = "Invalid request parameters"
		}
		endpoint.Response = fmt.Sprintf(`{"error":"Bad Request","message":"%s"}`, message)
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// Success Response Middleware

// WithCreated sets up a created response
func WithCreated(resourceID interface{}) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusCreated
		endpoint.Response = fmt.Sprintf(`{"id":%v,"message":"Resource created successfully"}`, resourceID)
		endpoint.Headers["Content-Type"] = "application/json"
		return endpoint
	}
}

// WithNoContent sets up a no content response
func WithNoContent() MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		endpoint.StatusCode = http.StatusNoContent
		endpoint.Response = ""
		return endpoint
	}
}

// Conditional Middleware

// WithConditionalResponse applies different middleware based on conditions
func WithConditionalResponse(condition func() bool, trueMiddleware, falseMiddleware MockMiddleware) MockMiddleware {
	return func(endpoint *MockEndpoint) *MockEndpoint {
		if condition() {
			return trueMiddleware(endpoint)
		}
		return falseMiddleware(endpoint)
	}
}
