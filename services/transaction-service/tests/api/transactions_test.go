package tests_api

// TODO: Needed extended requests for examining mock responses inside server,
// Mock responses should be refreshed in each tests
// TODO: Do the test on transactions

// import (
// 	"net/http"
// 	"net/http/httptest"
// 	"testing"
// 	"transaction-service/core/connect"

// 	"github.com/bmizerany/assert"
// )

// func TestTransactionEnpoint(t testing.T) {
// 	w := httptest.NewRecorder()
// 	req, _ := http.NewRequest(tt.method, tt.path, nil)
// 	router := connect.Router
// 	router.ServeHTTP(w, req)
// 	assert.Equal(t, tt.expected, w.Code, tt.path)
// }
