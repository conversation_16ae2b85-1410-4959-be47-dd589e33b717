package tests_api

import (
	"fmt"
	"os"
	"strings"
	"testing"
	"transaction-service/core/server"

	"github.com/gin-gonic/gin"
)

// InitTestRouter initializes the test environment and returns the router for testing
// This function handles the working directory change and server initialization
// needed for router-based testing without starting the full HTTP server
func InitTestRouter() (*gin.Engine, error) {
	// Change to service root directory to find config file
	// Note: We don't defer the directory change back since tests need to run in the service root
	currentDir, _ := os.Getwd()
	if !strings.HasSuffix(currentDir, "transaction-service") {
		os.Chdir("../..")
	}

	// Initialize test server to get the router
	s := &server.Server{Test: true}
	router, _, err := s.InitTest()
	if err != nil {
		return nil, fmt.Errorf("failed to initialize test server: %w", err)
	}

	return router, nil
}

func TestMain(m *testing.M) {
	// For router-based tests, we don't need the full TestMain setup
	// Just run the tests directly
	code := m.Run()
	os.Exit(code)
}
