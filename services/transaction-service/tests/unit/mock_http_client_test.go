package unit

import (
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"
	"transaction-service/tests/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMockHTTPClient_BasicFunctionality(t *testing.T) {
	client := mocks.NewMockHTTPClient()

	t.Run("successful response", func(t *testing.T) {
		// Setup mock response
		client.SetResponse("GET", "http://example.com/api", 200, "success")

		// Create request
		req, err := http.NewRequest("GET", "http://example.com/api", nil)
		require.NoError(t, err)

		// Execute request
		resp, err := client.Do(req)
		require.NoError(t, err)

		// Verify response
		assert.Equal(t, 200, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)
		assert.Equal(t, "success", string(body))
	})

	t.Run("error response", func(t *testing.T) {
		client.Reset()

		// Setup mock error
		testErr := errors.New("network error")
		client.SetError("GET", "http://example.com/error", testErr)

		// Create request
		req, err := http.NewRequest("GET", "http://example.com/error", nil)
		require.NoError(t, err)

		// Execute request
		resp, err := client.Do(req)

		// Verify error
		assert.Error(t, err)
		assert.Equal(t, testErr, err)
		assert.Nil(t, resp)
	})

	t.Run("request capture", func(t *testing.T) {
		client.Reset()

		// Setup mock response
		client.SetResponse("POST", "http://example.com/post", 201, "created")

		// Create request with body
		body := strings.NewReader("test body")
		req, err := http.NewRequest("POST", "http://example.com/post", body)
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer token123")

		// Execute request
		_, err = client.Do(req)
		require.NoError(t, err)

		// Verify request was captured
		requests := client.GetRequests()
		assert.Len(t, requests, 1)

		capturedReq := requests[0]
		assert.Equal(t, "POST", capturedReq.Method)
		assert.Equal(t, "http://example.com/post", capturedReq.URL)
		assert.Equal(t, "test body", capturedReq.Body)
		assert.Equal(t, "application/json", capturedReq.Headers["Content-Type"])
		assert.Equal(t, "Bearer token123", capturedReq.Headers["Authorization"])
	})

	t.Run("no mock response returns 404", func(t *testing.T) {
		client.Reset()

		// Create request without setting up mock
		req, err := http.NewRequest("GET", "http://example.com/notfound", nil)
		require.NoError(t, err)

		// Execute request
		resp, err := client.Do(req)
		require.NoError(t, err)

		// Should return 404
		assert.Equal(t, 404, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)
		assert.Equal(t, "Mock response not found", string(body))
	})
}

func TestMockHTTPClient_HelperMethods(t *testing.T) {
	client := mocks.NewMockHTTPClient()

	t.Run("SetSuccessResponse", func(t *testing.T) {
		client.SetSuccessResponse("GET", "http://example.com/success", "ok")

		req, _ := http.NewRequest("GET", "http://example.com/success", nil)
		resp, err := client.Do(req)
		require.NoError(t, err)

		assert.Equal(t, 200, resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		assert.Equal(t, "ok", string(body))
	})

	t.Run("SetErrorResponse", func(t *testing.T) {
		client.Reset()
		client.SetErrorResponse("GET", "http://example.com/error", 500, "server error")

		req, _ := http.NewRequest("GET", "http://example.com/error", nil)
		resp, err := client.Do(req)
		require.NoError(t, err)

		assert.Equal(t, 500, resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		assert.Equal(t, "server error", string(body))
	})

	t.Run("SetJSONResponse", func(t *testing.T) {
		client.Reset()
		client.SetJSONResponse("GET", "http://example.com/json", `{"key":"value"}`)

		req, _ := http.NewRequest("GET", "http://example.com/json", nil)
		resp, err := client.Do(req)
		require.NoError(t, err)

		assert.Equal(t, 200, resp.StatusCode)
		assert.Equal(t, "application/json", resp.Header.Get("Content-Type"))
		body, _ := io.ReadAll(resp.Body)
		assert.Equal(t, `{"key":"value"}`, string(body))
	})
}

func TestMockHTTPClient_RequestFiltering(t *testing.T) {
	client := mocks.NewMockHTTPClient()

	// Make multiple requests
	req1, _ := http.NewRequest("GET", "http://example.com/api/users", nil)
	req2, _ := http.NewRequest("POST", "http://example.com/api/users", nil)
	req3, _ := http.NewRequest("GET", "http://example.com/api/posts", nil)

	client.Do(req1)
	client.Do(req2)
	client.Do(req3)

	t.Run("GetRequestsForURL", func(t *testing.T) {
		userRequests := client.GetRequestsForURL("api/users")
		assert.Len(t, userRequests, 2)

		postRequests := client.GetRequestsForURL("api/posts")
		assert.Len(t, postRequests, 1)

		nonExistentRequests := client.GetRequestsForURL("api/nonexistent")
		assert.Len(t, nonExistentRequests, 0)
	})

	t.Run("GetRequestCount", func(t *testing.T) {
		assert.Equal(t, 3, client.GetRequestCount())
	})
}

func TestMockHTTPClient_ConcurrentAccess(t *testing.T) {
	client := mocks.NewMockHTTPClient()
	client.SetSuccessResponse("GET", "http://example.com/concurrent", "ok")

	// Test concurrent access
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func() {
			req, _ := http.NewRequest("GET", "http://example.com/concurrent", nil)
			_, err := client.Do(req)
			assert.NoError(t, err)
			done <- true
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	// Verify all requests were captured
	assert.Equal(t, 10, client.GetRequestCount())
}

func TestRestClientMock_AuthServiceMethods(t *testing.T) {
	t.Run("CreateTestAdmins", func(t *testing.T) {
		// Test the predefined test data creation
		testAdmins := mocks.CreateTestAdmins()
		assert.Len(t, testAdmins, 3)
		assert.Equal(t, uint(1), testAdmins[0].UserID)
		assert.Equal(t, "super_admin", testAdmins[0].Role)
	})

	t.Run("CreateSingleAdmin", func(t *testing.T) {
		admin := mocks.CreateSingleAdmin(123, "test_role")
		assert.Len(t, admin, 1)
		assert.Equal(t, uint(123), admin[0].UserID)
		assert.Equal(t, "test_role", admin[0].Role)
	})
}
