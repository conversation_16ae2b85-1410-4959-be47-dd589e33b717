package unit

import (
	"errors"
	"net/http"
	"testing"
	"transaction-service/core/connect"
	httpClient "transaction-service/core/http"
	"transaction-service/internal/integration/rest"
	"transaction-service/tests/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFetchUserRoles_Unit(t *testing.T) {
	// Save original client
	originalClient := connect.HTTPClient
	defer func() { connect.HTTPClient = originalClient }()

	setupMockClient := func() *mocks.MockHTTPClient {
		connect.HTTPClient = httpClient.NewSelectiveHTTPClient(&http.Client{})
		mockClient := mocks.NewMockHTTPClient()
		httpClient.SetMockClient(mockClient)
		return mockClient
	}

	t.Run("successful response", func(t *testing.T) {
		mockClient := setupMockClient()

		// Setup mock response
		jsonResponse := `[{"UserId":1,"Role":"admin"},{"UserId":2,"Role":"user"}]`
		mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", jsonResponse)

		// Call function
		admins, err := rest.FetchUserRoles()

		// Assertions
		require.NoError(t, err)
		assert.Len(t, admins, 2)
		assert.Equal(t, uint(1), admins[0].UserID)
		assert.Equal(t, "admin", admins[0].Role)
		assert.Equal(t, uint(2), admins[1].UserID)
		assert.Equal(t, "user", admins[1].Role)

		// Verify request was made
		requests := mockClient.GetRequests()
		assert.Len(t, requests, 1)
		assert.Equal(t, "GET", requests[0].Method)
		assert.Contains(t, requests[0].URL, "api/v1/user/get-all-admins")
	})

	t.Run("http error response", func(t *testing.T) {
		mockClient := setupMockClient()

		// Setup error response
		mockClient.SetErrorResponse("GET", "api/v1/user/get-all-admins", 500, "Internal Server Error")

		// Call function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "unexpected status code: 500")
	})

	t.Run("network error", func(t *testing.T) {
		mockClient := setupMockClient()

		// Setup network error
		networkErr := errors.New("connection refused")
		mockClient.SetError("GET", "api/v1/user/get-all-admins", networkErr)

		// Call function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "request failed")
		assert.Contains(t, err.Error(), "connection refused")
	})

	t.Run("invalid json response", func(t *testing.T) {
		mockClient := setupMockClient()

		// Setup invalid JSON response
		mockClient.SetSuccessResponse("GET", "api/v1/user/get-all-admins", "invalid json")

		// Call function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "failed to parse JSON")
	})

	t.Run("empty response", func(t *testing.T) {
		mockClient := setupMockClient()

		// Setup empty array response
		mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", "[]")

		// Call function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, admins, 0)
	})

	t.Run("authorization header is set", func(t *testing.T) {
		mockClient := setupMockClient()

		// Setup mock response
		mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", "[]")

		// Call function
		_, err := rest.FetchUserRoles()
		require.NoError(t, err)

		// Verify authorization header
		requests := mockClient.GetRequests()
		require.Len(t, requests, 1)

		authHeader, exists := requests[0].Headers["Authorization"]
		assert.True(t, exists, "Authorization header should be present")
		assert.Contains(t, authHeader, "Bearer", "Authorization header should contain Bearer token")
	})
}

func TestCentralizedHTTPClient(t *testing.T) {
	t.Run("selective mocking works", func(t *testing.T) {
		// Save original client
		originalClient := connect.HTTPClient
		defer func() { connect.HTTPClient = originalClient }()

		// Setup selective client
		connect.HTTPClient = httpClient.NewSelectiveHTTPClient(&http.Client{})
		mockClient := mocks.NewMockHTTPClient()

		// Initially no mock - should use real client
		assert.False(t, httpClient.IsMockEnabled())

		// Enable mock
		httpClient.SetMockClient(mockClient)
		assert.True(t, httpClient.IsMockEnabled())

		// Disable mock
		httpClient.DisableMock()
		assert.False(t, httpClient.IsMockEnabled())
	})
}
