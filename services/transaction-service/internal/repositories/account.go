package repositories

import (
	"transaction-service/internal/dto"
	"transaction-service/internal/models"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AccountRepositoryInterface interface {
	Create(account *models.Account) error
	GetOwnAccounts(ownerId uint, query *dto.AccountOwnQuery) ([]models.Account, error)
	GetAllAccounts(query *dto.AccountQuery, pag *dto.PaginationDto) (*dto.GetAllAccountDto, error)
	GetAccountById(id uint) (*models.Account, error)
	UpdateAccount(account *models.Account) error
	UpdateAccountAmount(tx *gorm.DB, account *models.Account) error
	UpdateAccountStatus(id uint, status string) error
	UpdateAmount(id uint, newAmount decimal.Decimal) error
	DeleteAccount(Id uint) error
}

type AccountRepository struct {
	db  *gorm.DB
	rds *redis.Client
}

func NewAccountRepository(rds *redis.Client, db *gorm.DB) *AccountRepository {
	return &AccountRepository{
		db:  db,
		rds: rds,
	}
}

func (ar *AccountRepository) Create(account *models.Account) error {
	return ar.db.Create(account).Error
}

func (ar *AccountRepository) GetOwnAccounts(ownerId uint, query *dto.AccountOwnQuery) ([]models.Account, error) {
	var accounts []models.Account
	queryModel := ar.db.Where("owner_id = ?", ownerId)
	if query != nil {
		if query.AssetType != "" {
			queryModel = queryModel.Where("asset_type = ?", query.AssetType)
		}
	}
	if err := queryModel.Preload(clause.Associations).Find(&accounts).Error; err != nil {
		return nil, err
	}
	return accounts, nil
}

func (ar *AccountRepository) GetAllAccounts(query *dto.AccountQuery, pag *dto.PaginationDto) (*dto.GetAllAccountDto, error) {
	var accounts []models.Account
	queryModel := ar.db.Model(&models.Account{})
	if query != nil {
		if query.OwnerId != 0 {
			queryModel = queryModel.Where("owner_id = ?", query.OwnerId)
		}
		if query.BankId != 0 {
			queryModel = queryModel.Where("bank_id = ?", query.BankId)
		}
		if query.CurrencyId != 0 {
			queryModel = queryModel.Where("currency_id = ?", query.CurrencyId)
		}
		if query.AssetType != "" {
			queryModel = queryModel.Where("asset_type = ?", query.AssetType)
		}
		if query.Status != "" {
			queryModel = queryModel.Where("status = ?", query.Status)
		}
	}
	var count int64
	if err := queryModel.Count(&count).Error; err != nil {
		return nil, err
	}
	if pag.Page != 0 && pag.Limit != 0 {
		if err := queryModel.Offset((pag.Page - 1) * pag.Limit).Limit(pag.Limit).Preload(clause.Associations).Find(&accounts).Error; err != nil {
			return nil, err
		}
	}
	var resp = dto.GetAllAccountDto{
		Count: count,
		Data:  accounts,
	}
	return &resp, nil
}

func (ar *AccountRepository) GetAccountById(id uint) (*models.Account, error) {
	var account models.Account
	if err := ar.db.Preload(clause.Associations).Preload("Fees.FeeType").First(&account, id).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

func (ar *AccountRepository) UpdateAccount(account *models.Account) error {
	return ar.db.Save(account).Error
}

func (ar *AccountRepository) UpdateAccountAmount(tx *gorm.DB, account *models.Account) error {
	return tx.Model(account).Update("current_amount", account.CurrentAmount).Error
}

func (ar *AccountRepository) UpdateAmount(id uint, newAmount decimal.Decimal) error {
	return ar.db.Model(&models.Account{}).Where("id = ?", id).Update("current_amount", newAmount).Error
}
func (ar *AccountRepository) UpdateAccountStatus(id uint, status string) error {
	return ar.db.Model(&models.Account{}).Where("id = ?", id).Update("status", status).Error
}

func (ar *AccountRepository) DeleteAccount(Id uint) error {
	return ar.db.Delete(&models.Account{}, Id).Error
}
