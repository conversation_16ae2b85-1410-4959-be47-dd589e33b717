package dto

import (
	"transaction-service/internal/models"

	"github.com/shopspring/decimal"
)

type AccountCreateDto struct {
	OwnerId           uint
	Fees              []models.Fee
	Name              string
	CurrencyId        uint
	BankId            uint
	AssetType         string
	Status            string
	BankNumber        string
	BankAccountNumber string
	CurrentAmount     decimal.Decimal
	BranchCode        string
	AccountOwner      string
	Reference         string
	BicSwift          string
}

type MinimalAccountCreateDto struct {
	OwnerId    uint
	CurrencyId uint
	Name       string
}

type AccountOwnQuery struct {
	AssetType string
}

type AccountQuery struct {
	OwnerId    uint
	AssetType  string
	BankId     uint
	CurrencyId uint
	Status     string
}

type GetAllAccountDto struct {
	Count int64
	Data  []models.Account
}

type AccountUpdateDto struct {
	Name              string
	OwnerId           uint
	Fees              []models.Fee
	CurrencyId        uint
	Currency          models.Currency
	BankId            *uint `gorm:"null"`
	Bank              models.Bank
	AssetType         string
	Status            string `gorm:"default:active"`
	AccountNumber     string `gorm:"unique;not null"`
	BankCountry       string
	BankNumber        string
	BankAddress       string
	BankAccountNumber string
	BranchCode        string
	AccountOwner      string
	Reference         string
	CurrentAmount     decimal.Decimal `gorm:"not null;default:0"`
	Iban              *models.Iban
	BicSwift          string
	QrCode            string
	AddressLink       string
	//	TODO implement other fields
}
