{"server": {"host": "0.0.0.0", "port": "8081"}, "database": {"host": "localhost", "port": 5434, "name": "monexa_test_db", "username": "postgres", "password": "fafQWllRTl4nfs", "sslMode": "disable"}, "rabbitMQ": {"host": "localhost", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "authService": {"token": "test-service-token", "host": "http://localhost:8091/"}, "dir": {"seeder": "./fixtures", "logPath": "./logs/test.log"}, "token": {"secretKey": "test-secret-key-for-testing-only"}, "redis": {"address": "localhost:6381", "db": 1, "password": "dfff34#2pl#ghl"}, "templatePath": {"rootPath": "./templates", "transactionReceipt": "transaction_cheque.html"}, "test": {"enabled": true, "mockAuth": true, "resetDatabase": true}}