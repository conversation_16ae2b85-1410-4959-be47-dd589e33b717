# Используем легковесный базовый образ для финальной стадии
FROM golang:latest as builder
ENV GO111MODULE=on
RUN apt-get update && apt-get install -y git && apt-get clean

WORKDIR /build
COPY go.mod go.sum ./
RUN go mod download

# Копируем исходный код и выполняем сборку
COPY . .
RUN make -B all

FROM builder
RUN apt-get -y install bash
RUN apt-get update
RUN apt-get -y install wkhtmltopdf

# Используем легковесный образ для финального контейнера
# FROM alpine:latest
WORKDIR /app


# Устанавливаем необходимые пакеты
# RUN apk add --no-cache bash fontconfig ttf-dejavu
# RUN apt-get -y install wkhtmltopdf



# Создаем директорию и копируем файлы из билдера
COPY --from=builder /build/bin/api ./
COPY build/local/config.docker.json ./config.json

# Устанавливаем команду по умолчанию (если необходимо)
CMD ["./api", "./seeder"]
