# Use Alpine for smaller image size
FROM golang:1.21-alpine as builder

# Install build dependencies
RUN apk add --no-cache git make

WORKDIR /build
COPY go.mod go.sum ./
RUN go mod download

# Copy source and build
COPY . .
RUN make -B all

# Final stage - use Alpine for minimal size
FROM alpine:3.18

# Install only essential runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    wkhtmltopdf \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy binaries and required files
COPY --from=builder /build/bin/api ./
COPY --from=builder /build/fixtures ./fixtures
COPY --from=builder /build/templates ./templates
COPY build/local/config.docker.json ./config.json

# Create logs directory
RUN mkdir -p /app/logs

# Use non-root user for security
RUN adduser -D -s /bin/sh appuser && chown -R appuser:appuser /app
USER appuser

CMD ["./api"]
