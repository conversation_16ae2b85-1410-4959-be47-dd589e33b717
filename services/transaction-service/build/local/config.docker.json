{"server": {"host": "0.0.0.0", "port": 9091}, "database": {"host": "transaction-postgres-db", "port": 5434, "name": "monexa_db", "username": "postgres", "password": "fafQWllRTl4nfs", "sslMode": "disable"}, "rabbitMQ": {"host": "transaction-rabbitmq", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "authService": {"token": "", "host": ""}, "token": {"secretKey": "project_secret_key"}, "redis": {"address": "transaction-redis:6379", "db": 0, "password": "dfff34#2pl#ghl"}, "templatePath": {"rootPath": "./templates", "transactionReceipt": "transaction_cheque.html"}}