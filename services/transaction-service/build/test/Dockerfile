# Build stage
FROM golang:latest as builder
ENV GO111MODULE=on
RUN apt-get update && apt-get install -y git && apt-get clean

WORKDIR /build
COPY go.mod go.sum ./
RUN go mod download

# Копируем исходный код и выполняем сборку
COPY . .
RUN make -B all

# Final stage
FROM ubuntu:22.04
RUN apt-get update && \
    apt-get install -y bash wkhtmltopdf && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Создаем необходимые директории
RUN mkdir -p /app/logs /app/fixtures /app/templates

# Копируем файлы из билдера
COPY --from=builder /build/bin/api ./
COPY --from=builder /build/fixtures ./fixtures
COPY --from=builder /build/templates ./templates
COPY build/test/config.docker.json ./config.json

# Устанавливаем команду по умолчанию
CMD ["./api"]